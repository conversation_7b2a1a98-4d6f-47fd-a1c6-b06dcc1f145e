package com.facishare.fmcg.provider.dao.po;

import java.util.Map;

import lombok.Data;
import lombok.ToString;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * 通用AI服务记录数据模型
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */
@Entity(value = "ai_service_record", noClassnameStored = true)
@Data
@ToString
public class AIServiceRecordPO {

    public static final String F_TENANT_ID = "TI";
    public static final String F_USER_ID = "UI";
    public static final String F_SERVICE_TYPE = "STP";
    public static final String F_BUSINESS_ID = "BI";
    public static final String F_MODEL_ID = "MI";
    public static final String F_RULE_ID = "RI";
    public static final String F_SCENE = "SC";
    public static final String F_METHOD_NAME = "MN";
    public static final String F_CLASS_NAME = "CN";
    public static final String F_INPUT_PARAMS = "IP";
    public static final String F_OUTPUT_RESULT = "OR";
    public static final String F_PROCESS_TIME = "PT";
    public static final String F_STATUS = "ST";
    public static final String F_ERROR_MSG = "EM";
    public static final String F_EXTRA_INFO = "EI";
    public static final String F_CREATE_TIME = "CT";
    public static final String F_UPDATE_TIME = "UT";
    public static final String F_CREATOR = "CTR";
    public static final String F_UPDATER = "UD";
    public static final String F_IS_DELETED = "ID";

    @Id
    private ObjectId id;

    /**
     * 租户ID
     */
    @Property(F_TENANT_ID)
    private Integer tenantId;

    /**
     * 用户ID
     */
    @Property(F_USER_ID)
    private Integer userId;

    /**
     * 服务类型
     * 例如：DETECT（识别）、GENERATE（生成）、ANALYZE（分析）等
     */
    @Property(F_SERVICE_TYPE)
    private String serviceType;

    /**
     * 业务ID
     * 用于标识业务唯一性
     */
    @Property(F_BUSINESS_ID)
    private String businessId;

    /**
     * 模型ID
     */
    @Property(F_MODEL_ID)
    private String modelId;

    /**
     * 规则ID
     */
    @Property(F_RULE_ID)
    private String ruleId;

    /**
     * 场景
     */
    @Property(F_SCENE)
    private String scene;

    /**
     * 方法名
     */
    @Property(F_METHOD_NAME)
    private String methodName;

    /**
     * 类名
     */
    @Property(F_CLASS_NAME)
    private String className;

    /**
     * 输入参数
     */
    @Property(F_INPUT_PARAMS)
    private String inputParams;

    /**
     * 输出结果
     */
    @Property(F_OUTPUT_RESULT)
    private String outputResult;

    /**
     * 处理时间(毫秒)
     */
    @Property(F_PROCESS_TIME)
    private Long processTime;

    /**
     * 状态：-1 待识别 0-成功，1-失败
     */
    @Property(F_STATUS)
    private Integer status;

    /**
     * 错误信息
     */
    @Property(F_ERROR_MSG)
    private String errorMsg;

    /**
     * 额外信息
     */
    @Property(F_EXTRA_INFO)
    private Map<String, Object> extraInfo;

    /**
     * 创建时间
     */
    @Property(F_CREATE_TIME)
    private Long createTime;

    /**
     * 更新时间
     */
    @Property(F_UPDATE_TIME)
    private Long updateTime;

    /**
     * 创建人
     */
    @Property(F_CREATOR)
    private String creator;

    /**
     * 更新人
     */
    @Property(F_UPDATER)
    private String updater;

    /**
     * 是否删除：0-正常，-1-删除
     */
    @Property(F_IS_DELETED)
    private String isDeleted;
} 