package com.facishare.fmcg.provider.impl.record;

import com.facishare.fmcg.api.annotation.AIServiceRecord;
import com.facishare.fmcg.provider.dao.po.AIServiceRecordPO;

import java.util.List;

/**
 * AI服务记录服务接口
 *
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */
public interface AIServiceRecordService {

    /**
     * 保存服务记录
     *
     * @param record 服务记录
     */
    void saveRecord(AIServiceRecordPO record);

    /**
     * 异步保存服务记录
     *
     * @param record 服务记录
     */
    void saveRecordAsync(AIServiceRecordPO record);

    /**
     * 更新服务记录
     *
     * @param record 服务记录
     */
    void updateRecord(AIServiceRecordPO record);

    /**
     * 异步更新服务记录
     *
     * @param record 服务记录
     */
    void updateRecordAsync(AIServiceRecordPO record);

    /**
     * 根据ID查询服务记录
     *
     * @param id 记录ID
     * @return 服务记录
     */
    AIServiceRecordPO getById(String id);

    /**
     * 根据业务ID查询服务记录
     *
     * @param businessId 业务ID
     * @return 服务记录列表
     */
    List<AIServiceRecordPO> getByBusinessId(String businessId);

    /**
     * 根据租户ID和用户ID查询服务记录
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 服务记录列表
     */
    List<AIServiceRecordPO> queryByUser(Integer tenantId, Integer userId, Long startTime, Long endTime, Integer limit, Integer offset);

    /**
     * 根据租户ID查询服务记录
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 服务记录列表
     */
    List<AIServiceRecordPO> queryByTenant(Integer tenantId, Long startTime, Long endTime, Integer limit, Integer offset);

    /**
     * 统计用户服务调用次数
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param serviceType 服务类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用次数
     */
    Long countByUser(Integer tenantId, Integer userId, String serviceType, Long startTime, Long endTime);

    /**
     * 统计租户服务调用次数
     *
     * @param tenantId 租户ID
     * @param serviceType 服务类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用次数
     */
    Long countByTenant(Integer tenantId, String serviceType, Long startTime, Long endTime);

    /**
     * 检查服务限制
     *
     * @param record 服务记录
     * @param aiServiceRecord 注解配置
     * @return 是否允许调用
     */
    boolean checkServiceLimit(AIServiceRecordPO record, AIServiceRecord aiServiceRecord);
}