package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.model.*;
import com.facishare.fmcg.api.service.ai.model.AIModelService;
import com.facishare.fmcg.provider.constant.ModelSceneEnum;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Component("storeFrontDetectLicenseHandler")
public class StoreFrontDetectLicenseHandler extends ModuleLicenseHandlerBase {

    public static final String STORE_FRONT_MODEL = "storeFrontModel";
    @Resource
    private PromptTemplateDAO promptTemplateDAO;

    @Resource
    private AIModelService aiModelService;

    @Resource
    private EIEAConverter eieaConverter;

    private static final String STORE_FRONT_PROMPT_TEMPLATE_CODE = "STORE_RECOGNITION_TEMPLATE";
    private static final String STORE_FRONT_NAME_MATCH_TEMPLATE_CODE = "STORE_RECOGNITION_AND_NAME_MATCH_TEMPLATE";
    private static final String STORE_FRONT_MODEL_NAME = "门头照识别";
    private static final String STORE_FRONT_AI_RULE_NAME_1 = "新建/编辑门店识别门头照信息";
    private static final String STORE_FRONT_AI_RULE_NAME_2 = "外勤拍门头验真";

    @Override
    public String getAppCode() {
        return "FMCG.STORE_FRONT_DETECT";
    }

    @Override
    public boolean validate(int tenantId, String tenantAccount, int userId) {
        return super.validate(tenantId, tenantAccount, userId);
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return tenantId + "." + userId;
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("开始激活门头照识别功能，tenantId: {}, userId: {}", tenantId, userId);

        try {
            // 1. 初始化提示词模板（2个模板）
            initPromptTemplates(tenantId, userId);

            // 2. 初始化门头照识别模型
            String modelId = initStoreFrontModel(tenantId, userId);

            // 3. 初始化AI规则，默认开启门头照识别（2条规则）
            initAIRules(tenantId, userId, modelId);

            log.info("门头照识别功能激活完成，tenantId: {}", tenantId);
        } catch (Exception e) {
            log.error("激活门头照识别功能失败，tenantId: {}, userId: {}", tenantId, userId, e);
            throw new RuntimeException("激活门头照识别功能失败", e);
        }

        super.active(tenantId, tenantAccount, userId);
    }

    /**
     * 初始化提示词模板（2个模板）
     */
    private void initPromptTemplates(int tenantId, int userId) {
        try {
            // 1. 初始化基础门店识别提示词模板
            initSinglePromptTemplate(tenantId, userId,
                    "model/storeFrontDetectPrompt.json",
                    STORE_FRONT_PROMPT_TEMPLATE_CODE,
                    "门店识别提示词模板");

            // 2. 初始化门店识别以及店名匹配提示词模板
            initSinglePromptTemplate(tenantId, userId,
                    "model/storeFrontDetectAndNameMatchPrompt.json",
                    STORE_FRONT_NAME_MATCH_TEMPLATE_CODE,
                    "门店识别以及店名匹配提示词模板");

        } catch (Exception e) {
            log.error("初始化提示词模板失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化提示词模板失败", e);
        }
    }

    /**
     * 初始化单个提示词模板
     */
    private void initSinglePromptTemplate(int tenantId, int userId, String configPath, String templateCode, String templateName) {
        try {
            // 检查是否已存在提示词模板
            PromptTemplatePO existingTemplate = promptTemplateDAO.getByCode(-1, templateCode);
            if (existingTemplate != null) {
                log.info("{}已存在，无需创建，tenantId: {}", templateName, tenantId);
                return;
            }

            // 从配置文件读取默认模板配置
            PromptTemplatePO templatePO = loadPromptTemplateFromConfig(configPath, templateCode);
            if (templatePO == null) {
                log.error("无法从配置文件加载{}，tenantId: {}", templateName, tenantId);
                return;
            }

            // 设置租户和用户信息
            templatePO.setTenantId(-1);  // 全局模板
            templatePO.setCreator(-10000);  // 系统用户
            templatePO.setLastUpdater(-10000);

            // 保存模板
            String templateId = promptTemplateDAO.save(templatePO);
            log.info("成功创建{}，tenantId: {}, templateId: {}", templateName, tenantId, templateId);

        } catch (Exception e) {
            log.error("初始化{}失败，tenantId: {}", templateName, tenantId, e);
            throw new RuntimeException("初始化" + templateName + "失败", e);
        }
    }

    /**
     * 初始化门头照识别模型
     * TODO: 如果已经存在门头模型就不进行预置
     */
    private String initStoreFrontModel(int tenantId, int userId) {
        try {
            log.info("开始初始化门头照识别模型，tenantId: {}", tenantId);

            // 1. 检查是否已存在门头照识别模型
            String existingModelId = checkExistingStoreFrontModel(tenantId);
            if (existingModelId != null) {
                log.info("门头照识别模型已存在，无需创建，tenantId: {}, modelId: {}", tenantId, existingModelId);
                return existingModelId;
            }

            // 2. 创建新的门头照识别模型
            log.info("未找到现有门头照识别模型，开始创建新模型，tenantId: {}", tenantId);
            ModelDTO modelDTO = createStoreFrontModelDTO(tenantId, userId);

            ApiArg<AddModel.Arg> addModelArg = new ApiArg<>();
            addModelArg.setTenantId(tenantId);
            addModelArg.setUserId(userId);

            AddModel.Arg arg = new AddModel.Arg();
            arg.setModel(modelDTO);
            addModelArg.setData(arg);

            initToken();

            ApiResult<AddModel.Result> result = aiModelService.addModel(addModelArg);
            if (result != null && result.getCode() == 0) {
                String modelId = result.getData().getModel().getId();
                log.info("成功创建门头照识别模型，tenantId: {}, modelId: {}", tenantId, modelId);
                return modelId;
            } else {
                log.error("创建门头照识别模型失败，返回结果为空，tenantId: {},result:{}", tenantId, result);
                throw new RuntimeException("创建门头照识别模型失败");
            }

        } catch (Exception e) {
            log.error("初始化门头照识别模型失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化门头照识别模型失败", e);
        }
    }

    private void initToken() {
        AddToken.Arg addTokenArg = new AddToken.Arg();
        TokenInfoDTO tokenInfo = new TokenInfoDTO();
        tokenInfo.setType("default");
        tokenInfo.setToken(decodeToken("NzcxNzgwZGItZjBiZC00Y2FjLTkyNzktNjFmMjE0YzM0MWI5"));
        tokenInfo.setIdentityKey("fx-kx-huoshan");

        addTokenArg.setTokenInfo(tokenInfo);
        ApiArg<AddToken.Arg> arg = new ApiArg<>();
        arg.setData(addTokenArg);
        arg.setTenantId(-1);
        ApiResult<AddToken.Result> result = aiModelService.addToken(arg);
        log.info("result:{}", result);
    }

    private String decodeToken(String token) {
        return new String(Base64.getDecoder().decode(token));
    }

    /**
     * 检查是否已存在门头照识别模型
     *
     * @param tenantId 租户ID
     * @return 如果存在返回模型ID，否则返回null
     */
    private String checkExistingStoreFrontModel(int tenantId) {
        try {
            // 查询storeFront场景下的所有模型
            ApiArg<GetAIModelsByScene.Arg> queryArg = new ApiArg<>();
            queryArg.setTenantId(tenantId);
            queryArg.setUserId(-10000); // 系统用户

            GetAIModelsByScene.Arg arg = new GetAIModelsByScene.Arg();
            arg.setScene("storeFront");
            queryArg.setData(arg);

            GetAIModelsByScene.Result result = aiModelService.getAIModelsByScene(queryArg).getData();
            if (result != null && result.getModels() != null) {
                // 检查是否存在同名的门头照识别模型
                for (ModelDTO model : result.getModels()) {
                    if (STORE_FRONT_MODEL.equals(model.getApiName())) {
                        log.info("找到现有门头照识别模型，modelId: {}, name: {}", model.getId(), model.getName());
                        return model.getId();
                    }
                }
            }

            log.info("未找到现有门头照识别模型，tenantId: {}", tenantId);
            return null;

        } catch (Exception e) {
            log.warn("检查现有门头照识别模型时发生异常，将继续创建新模型，tenantId: {}", tenantId, e);
            return null;
        }
    }

    /**
     * 创建门头照识别模型DTO
     */
    private ModelDTO createStoreFrontModelDTO(int tenantId, int userId) {
        ModelDTO modelDTO = new ModelDTO();
        modelDTO.setTenantId(tenantId);
        modelDTO.setName(STORE_FRONT_MODEL_NAME);
        modelDTO.setScene(ModelSceneEnum.DOOR_PHOTO_RECOGNITION.code());
        modelDTO.setApiName(STORE_FRONT_MODEL);
        modelDTO.setType("storeFrontDetect");
        modelDTO.setParentType("LLM");
        modelDTO.setStatus(1);
        modelDTO.setDefault(true);
        modelDTO.setPlatform("openai_vlm");
        modelDTO.setModelManufacturer("fs");
        modelDTO.setDescription("通过纷享自研图像模型快速识别创建门店拍摄的门头照片，提取门头照内容信息");

        // 设置参数
        JSONObject params = new JSONObject();
        modelDTO.setParams(params);
        params.put("url", "https://ark.cn-beijing.volces.com/api/v3/chat/completions");
        params.put("model", "doubao-seed-1-6-flash-250715");
        params.put("response_format", new JSONObject().fluentPut("type", "json_schema"));
        params.put("thinking", new JSONObject().fluentPut("type", "disabled"));


        modelDTO.setTokenInfo(null);
        modelDTO.setToken_identityKey("fx-kx-huoshan");

        // 设置AI规则能力
        List<String> capabilities = new ArrayList<>();
        capabilities.add("isOpenStorefrontDetect");
        modelDTO.setAiRuleCapabilities(capabilities);

        return modelDTO;
    }

    /**
     * 从配置文件加载提示词模板
     */
    private PromptTemplatePO loadPromptTemplateFromConfig(String configPath, String templateCode) {
        try {
            ClassPathResource resource = new ClassPathResource(configPath);
            if (!resource.exists()) {
                log.error("配置文件不存在: {}", configPath);
                return null;
            }

            // 读取配置文件内容
            byte[] bytes = new byte[(int) resource.contentLength()];
            resource.getInputStream().read(bytes);
            String jsonContent = new String(bytes, StandardCharsets.UTF_8);

            // 解析JSON配置
            PromptTemplatePO templatePO = JSON.parseObject(jsonContent, PromptTemplatePO.class);

            // 设置正确的模板代码（覆盖JSON中的code字段）
            templatePO.setCode(templateCode);

            log.info("成功从配置文件加载提示词模板: {}, code: {}", templatePO.getName(), templateCode);
            return templatePO;

        } catch (IOException e) {
            log.error("读取配置文件失败: {}", configPath, e);
            return null;
        } catch (Exception e) {
            log.error("解析配置文件失败: {}", configPath, e);
            return null;
        }
    }

    /**
     * 初始化AI规则，默认开启门头照识别（2条规则）
     */
    private void initAIRules(int tenantId, int userId, String modelId) {
        try {
            // 1. 创建第一条AI规则：新建/编辑门店识别门头照信息
            createSingleAIRule(tenantId, userId, modelId,
                    STORE_FRONT_AI_RULE_NAME_1,
                    STORE_FRONT_PROMPT_TEMPLATE_CODE,
                    "业务员开拓新店，拍摄门头照通过AI验真门头照及自动录入门店信息",
                    "StoreFrontDetect");


            // 2. 创建第二条AI规则：外勤拍门头验真
            createSingleAIRule(tenantId, userId, modelId,
                    STORE_FRONT_AI_RULE_NAME_2,
                    STORE_FRONT_NAME_MATCH_TEMPLATE_CODE,
                    "业务员日常巡店，拍门头照打卡通过AI验真",
                    "StoreFrontDetectAndMatchName");


        } catch (Exception e) {
            log.error("初始化门头照识别AI规则失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化门头照识别AI规则失败", e);
        }
    }

    /**
     * 创建单条AI规则
     * TODO: 如果已经存在同名AI规则就不进行预置
     */
    private void createSingleAIRule(int tenantId, int userId, String modelId, String ruleName, String templateCode, String ruleDescription, String apiName) {
        try {
            // 1. 检查是否已存在同名AI规则
            String existingRuleId = checkExistingAIRule(tenantId, modelId, apiName);
            if (existingRuleId != null) {
                log.info("AI规则已存在，无需创建，ruleName: {}, tenantId: {}, ruleId: {}", ruleName, tenantId, existingRuleId);
                return;
            }

            // 2. 创建新的AI规则
            log.info("未找到现有AI规则，开始创建新规则，ruleName: {}, tenantId: {}", ruleName, tenantId);
            AIDetectRuleDTO ruleDTO = createStoreFrontAIRuleDTO(tenantId, userId, modelId, ruleName, templateCode, ruleDescription, apiName);

            ApiArg<SaveOrUpdateDetectRule.Arg> saveRuleArg = new ApiArg<>();
            saveRuleArg.setTenantId(tenantId);
            saveRuleArg.setUserId(userId);

            SaveOrUpdateDetectRule.Arg arg = new SaveOrUpdateDetectRule.Arg();
            arg.setAiDetectRuleDTO(ruleDTO);
            saveRuleArg.setData(arg);

            SaveOrUpdateDetectRule.Result result = aiModelService.saveOrUpdateDetectRule(saveRuleArg).getData();
            if (result != null && result.getRule() != null) {
                String ruleId = result.getRule().getId();
                log.info("成功创建AI规则：{}，tenantId: {}, ruleId: {}", ruleName, tenantId, ruleId);
            } else {
                log.error("创建AI规则失败，返回结果为空，ruleName: {}, tenantId: {}", ruleName, tenantId);
                throw new RuntimeException("创建AI规则失败：" + ruleName);
            }

        } catch (Exception e) {
            log.error("创建AI规则失败，ruleName: {}, tenantId: {}", ruleName, tenantId, e);
            throw new RuntimeException("创建AI规则失败：" + ruleName, e);
        }
    }


    @Override
    public String upgrade(int tenantId) {
        this.active(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), -10000);
        return "";
    }

    /**
     * 检查是否已存在同名AI规则
     *
     * @param tenantId 租户ID
     * @param modelId  模型ID
     * @param apiName  apiName
     * @return 如果存在返回规则ID，否则返回null
     */
    private String checkExistingAIRule(int tenantId, String modelId, String apiName) {
        try {
            // 查询指定模型下的所有AI规则
            ApiArg<QueryRuleList.Arg> queryArg = new ApiArg<>();
            queryArg.setTenantId(tenantId);
            queryArg.setUserId(-10000); // 系统用户

            QueryRuleList.Arg arg = new QueryRuleList.Arg();
            arg.setModelId(modelId);
            queryArg.setData(arg);

            ApiResult<QueryRuleList.Result> result = aiModelService.queryRuleList(queryArg);
            if (result != null && result.getCode() == 0) {
                // 检查是否存在同名的AI规则
                for (AIDetectRuleDTO rule : result.getData().getRules()) {
                    if (apiName.equals(rule.getApiName())) {
                        log.info("找到现有AI规则，ruleId: {}, name: {}", rule.getId(), rule.getName());
                        return rule.getId();
                    }
                }
            }

            log.info("未找到现有AI规则，ruleName: {}, modelId: {}, tenantId: {}", apiName, modelId, tenantId);
            return null;

        } catch (Exception e) {
            log.warn("检查现有AI规则时发生异常，将继续创建新规则，apiName: {}, modelId: {}, tenantId: {}", apiName, modelId, tenantId, e);
            return null;
        }
    }

    /**
     * 创建门头照识别AI规则DTO（通用方法）
     */
    private AIDetectRuleDTO createStoreFrontAIRuleDTO(int tenantId, int userId, String modelId, String ruleName, String templateCode, String ruleDescription, String apiName) {
        AIDetectRuleDTO ruleDTO = new AIDetectRuleDTO();
        ruleDTO.setTenantId(tenantId);
        ruleDTO.setName(ruleName);
        ruleDTO.setApiName(apiName);
        ruleDTO.setPromptTemplate(templateCode);
        ruleDTO.setModelId(modelId);
        ruleDTO.setDefault(true);
        ruleDTO.setRuleDescribe(ruleDescription);

        // 设置字段映射
        Map<String, FieldDTO> fieldMap = new HashMap<>();

        // 根据规则名称设置不同的API名称和主对象
        if (STORE_FRONT_AI_RULE_NAME_1.equals(ruleName)) {
            ruleDTO.setMasterDescribeApiName("AccountObj");
            // 门店名称字段
            FieldDTO storeNameField = new FieldDTO();
            storeNameField.setType("mapping");
            storeNameField.setFieldKey("storeName");
            storeNameField.setObjectApiName("AccountObj");
            storeNameField.setFieldType("text");
            storeNameField.setAiStoreFieldApiName("ai_recognized_store_name");
            storeNameField.setManuallyStoreFieldApiName("name");
            fieldMap.put("storeName", storeNameField);

            // 是否门头照字段
            FieldDTO isStorefrontField = new FieldDTO();
            isStorefrontField.setType("mapping");
            isStorefrontField.setFieldKey("isStorefront");
            isStorefrontField.setObjectApiName("AccountObj");
            fieldMap.put("isStorefront", isStorefrontField);

            // 是否门头照字段
            /*FieldDTO isStorefrontMatchField = new FieldDTO();
            isStorefrontMatchField.setType("mapping");
            isStorefrontMatchField.setFieldKey("isMatchStorefrontName");
            isStorefrontMatchField.setObjectApiName("AccountObj");
            isStorefrontMatchField.setFieldType("true_or_false");
            isStorefrontMatchField.setAiStoreFieldApiName("ai_recognized_store_name_match");
            fieldMap.put("isMatchStorefrontName", isStorefrontMatchField);*/

        } else if (STORE_FRONT_AI_RULE_NAME_2.equals(ruleName)) {
            ruleDTO.setMasterDescribeApiName("CheckinsObj");
        }

        // 设置检测能力映射
        Map<String, Integer> detectCapabilityMap = new HashMap<>();
        detectCapabilityMap.put("isOpenStorefrontDetect", 1);
        ruleDTO.setDetectCapabilityMap(detectCapabilityMap);

        ruleDTO.setFieldMap(fieldMap);

        return ruleDTO;
    }

}