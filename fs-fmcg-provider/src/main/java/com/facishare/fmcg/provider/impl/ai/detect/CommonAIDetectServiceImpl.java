package com.facishare.fmcg.provider.impl.ai.detect;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.adapter.ai.AIServiceAdapter;
import com.facishare.fmcg.api.annotation.AIServiceRecord;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.detect.CommonAIDetect;
import com.facishare.fmcg.api.dto.ai.model.AIDetectRuleDTO;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.detect.CommonAIDetectService;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import com.fs.fmcg.sdk.ai.adapter.IVLMDetector;
import com.fs.fmcg.sdk.ai.adapter.contract.AuthenticationInfoDTO;
import com.fs.fmcg.sdk.ai.adapter.contract.FileDTO;
import com.fs.fmcg.sdk.ai.adapter.contract.LLMMessageDTO;
import com.fs.fmcg.sdk.ai.adapter.contract.TextDTO;
import com.fs.fmcg.sdk.ai.adapter.contract.VLMDetect;
import com.fs.fmcg.sdk.ai.factory.VLMDetectorFactory;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 门头识别服务实现类
 *
 * <AUTHOR> Assistant
 * @date 2025-07-17
 */
@Service
@Slf4j
public class CommonAIDetectServiceImpl extends ServiceBase implements CommonAIDetectService {

    @Resource
    private VLMDetectorFactory vlmDetectorFactory;

    @Resource
    private AIServiceAdapter aiServiceAdapter;

    @Resource
    private TokenFactory tokenFactory;

    @Resource
    private PromptTemplateDAO promptTemplateDAO;

    @Resource
    private CommonAIDetectServiceImpl commonAIDetectServiceImpl;

    @Override
    public ApiResult<CommonAIDetect.Result> commonAIDetect(ApiArg<CommonAIDetect.Arg> arg) {
        log.info("[StoreFrontDetect] 开始门头识别，参数: {}", arg);
        try {
            CommonAIDetect.Arg requestArg = arg.getData();

            changeScene(requestArg);

            // 1. 参数验证
            requestArg.validate();

            // 2. 调用AI模型进行门头识别
            CommonAIDetect.Result result = commonAIDetectServiceImpl.detect(arg.getTenantId(), arg.getUserId(), requestArg.getNPath(), arg.getData().getModelId(), arg.getData().getRuleId(), requestArg.getScene(), arg.getData().getExtraParams());

            log.info("[StoreFrontDetect] 门头识别完成，结果: {}", result);
            return setSuccess(result);

        } catch (FmcgException fmcgException) {
            log.info("门头识别异常:", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("[StoreFrontDetect] 门头识别失败", e);
            return setFailure(ErrorCode.STORE_FRONT_DETECT_ERROR);
        }
    }

    private void changeScene(CommonAIDetect.Arg requestArg) {
        if ("doorPhotoRecognition".equals(requestArg.getScene())) {
            requestArg.setScene("storeFront");
        } else if ("recapture".equals(requestArg.getScene())) {
            requestArg.setScene("remakeDetection");
        }
    }

    @AIServiceRecord(
            serviceType = "DETECT",
            businessId = "'commonAIDetect_'+ #modelId + '_' + #ruleId + '_' + #nPath",
            description = "通用AI识别服务",
            enableLimit = false,
            dailyLimit = 1000,
            monthlyLimit = 10000,
            enableCache = true,
            cacheExpireTime = -1,
            tenantId = "#tenantId",
            userId = "#userId",
            modelId = "#modelId",
            ruleId = "#ruleId",
            scene = "#scene"
    )
    public CommonAIDetect.Result detect(Integer tenantId, Integer userId, String nPath, String modelId, String ruleId, String scene, Map<String, Object> extraParams) {
        GetModelById.Result modelResult = aiServiceAdapter.getModelById(tenantId, modelId, false, false, false);
        ModelDTO model = modelResult.getModel();
        validateScene(model, scene);
        AIDetectRuleDTO aiDetectRuleDTO = aiServiceAdapter.getAIDetectRuleById(tenantId, ruleId);
        String platform = Strings.isNullOrEmpty(model.getPlatform()) ? "fshare_vlm" : model.getPlatform();
        if (!"LLM".equals(model.getParentType())) {
            throw new FmcgException("暂不支持其他类型的模型", ErrorCode.AI_MODEL_TYPE_NOT_SUPPORTED.getCode());
        }
        IVLMDetector detector = vlmDetectorFactory.getVLMDetector(platform);
        AuthenticationInfoDTO auth = formAuth(tenantId, userId, model);

        VLMDetect.Arg detectArg = formDetectArg(nPath, model, aiDetectRuleDTO, extraParams);

        VLMDetect.Result vlmResult = detector.detect(auth, detectArg);
        if (vlmResult.getErrorCode() != 0) {
            log.info("detect err,arg:{},rst:{}", detectArg, vlmResult);
            throw new FmcgException(vlmResult.getErrorMessage(), vlmResult.getErrorCode());
        }
        CommonAIDetect.Result result = new CommonAIDetect.Result();
        result.setDetectDetail(new JSONObject(vlmResult.getResult()));
        result.setRuleDetectResult(fillRuleDetectResult(result, aiDetectRuleDTO));
        return result;
    }

    private void fillResponseFormat(VLMDetect.ResponseFormat responseFormat, PromptTemplatePO promptTemplatePO) {
        if (!responseFormat.getType().equals("json_schema")) {
            return;
        }
        JSONObject jsonSchema = new JSONObject();
        jsonSchema.put("name", "result");
        jsonSchema.put("strict", true);
        JSONObject schema = new JSONObject();
        jsonSchema.put("schema", schema);
        schema.put("type", "object");
        JSONObject properties = new JSONObject();
        schema.put("properties", properties);
        List<String> required = new ArrayList<>();
        promptTemplatePO.getOutputParameters().forEach(param -> {
            if (param.getRequired()) {
                required.add(param.getName());
            }
            properties.put(param.getName(), new JSONObject().fluentPut("type", param.getType().toLowerCase()));
        });
        schema.put("required", required);
        schema.put("additionalProperties", false);
        responseFormat.setJsonSchema(jsonSchema);
    }

    private JSONObject fillRuleDetectResult(CommonAIDetect.Result result, AIDetectRuleDTO aiDetectRuleDTO) {
        JSONObject ruleDetail = new JSONObject();
        if (MapUtils.isNotEmpty(result.getRuleDetectResult())) {
            aiDetectRuleDTO.getFieldMap().forEach((key, value) -> {
                if (!Strings.isNullOrEmpty(value.getAiStoreFieldApiName()) && result.getDetectDetail().containsKey(key)) {
                    ruleDetail.put(value.getAiStoreFieldApiName(), result.getDetectDetail().get(key));
                }
            });
        }
        return ruleDetail;
    }

    private VLMDetect.Arg formDetectArg(String nPath, ModelDTO model, AIDetectRuleDTO aiDetectRuleDTO, Map<String, Object> extraParams) {
        VLMDetect.Arg detectArg = new VLMDetect.Arg();

        detectArg.setModel(model.getParams().getString("model"));
        PromptTemplatePO promptTemplatePO = getPromptByCode(model.getTenantId(), aiDetectRuleDTO.getPromptTemplate());
        if (promptTemplatePO == null) {
            throw new FmcgException("找不到提示词", ErrorCode.PROMPT_TEMPLATE_NOT_FOUND.getCode());
        }
        extraParams.put("nPath", nPath);
        extraParams.putAll(model.getParams());
        List<LLMMessageDTO> messageDTOList = new ArrayList<>();
        LLMMessageDTO mainMessage = translatePrompt2LLMMessage(promptTemplatePO, extraParams);
        messageDTOList.add(mainMessage);
        messageDTOList.addAll(promptTemplatePO.getAuxiliaryPrompt().stream().map(pmt -> translatePrompt2LLMMessage(pmt, extraParams)).collect(Collectors.toList()));
        detectArg.setMessages(messageDTOList);
        detectArg.setExtraData(model.getParams());
        detectArg.setUrl(model.getParams().getString("url"));
        if (model.getParams().containsKey("thinking")) {
            detectArg.setThinking(model.getParams().getJSONObject("thinking").toJavaObject(VLMDetect.Thinking.class));
        }
        if (model.getParams().containsKey("response_format")) {
            detectArg.setResponseFormat(model.getParams().getJSONObject("response_format").toJavaObject(VLMDetect.ResponseFormat.class));
            fillResponseFormat(detectArg.getResponseFormat(), promptTemplatePO);
        }
        return detectArg;
    }

    private PromptTemplatePO getPromptByCode(Integer tenantId, String code) {
        PromptTemplatePO promptTemplatePO = promptTemplateDAO.getByCode(tenantId, code);
        if (promptTemplatePO == null) {
            return promptTemplateDAO.getByCode(-1, code);
        }
        return promptTemplatePO;
    }

    private LLMMessageDTO translatePrompt2LLMMessage(PromptTemplatePO promptTemplatePO, Map<String, Object> extraParams) {
        LLMMessageDTO messageDTO = new LLMMessageDTO();
        messageDTO.setRole(promptTemplatePO.getRole());
        messageDTO.setContents(Lists.newArrayList());
        if ("text".equals(promptTemplatePO.getPromptType())) {
            TextDTO textDTO = new TextDTO();
            textDTO.setType("text");
            String tmpPrompt = makeUpPrompt(promptTemplatePO, extraParams);
            textDTO.setText(tmpPrompt);
            messageDTO.getContents().add(textDTO);
        } else if ("image".equals(promptTemplatePO.getPromptType())) {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setType("image");
            String tmpPrompt = makeUpPrompt(promptTemplatePO, extraParams);
            fileDTO.setPath(tmpPrompt);
            messageDTO.getContents().add(fileDTO);
        } else {
            log.info("prompt type:{}", promptTemplatePO.getPromptType());
            throw new FmcgException("未知提示词类型", ErrorCode.UNKNOWN_PROMPT_TYPE.getCode());
        }
        return messageDTO;
    }

    private String makeUpPrompt(PromptTemplatePO promptTemplatePO, Map<String, Object> extraParams) {
        String prompt = promptTemplatePO.getPromptText();
        if (!CollectionUtils.isEmpty(promptTemplatePO.getInputParameters())) {
            for (PromptTemplatePO.ParameterDefinition param : promptTemplatePO.getInputParameters()) {
                Object value = extraParams.getOrDefault(param.getName(), param.getDefaultValue());
                if (param.getRequired() && value == null) {
                    throw new FmcgException(String.format("缺少必填参数:%s", param.getName()), ErrorCode.MISSING_REQUIRED_PARAMETER.getCode());
                }
                prompt = prompt.replaceAll("#\\{" + param.getName() + "}", String.valueOf(value));
            }
        }
        return prompt;
    }

    private void validateScene(ModelDTO model, String scene) {
        if (!scene.equals(model.getScene())) {
            throw new FmcgException("模型场景不匹配", ErrorCode.MODEL_SCENE_MISMATCH.getCode());
        }
    }

    private AuthenticationInfoDTO formAuth(Integer tenantId, Integer userId, ModelDTO model) {
        AuthenticationInfoDTO auth = new AuthenticationInfoDTO();
        auth.setTenantId(tenantId);
        auth.setUserId(userId);
        if (model.getTokenInfo() != null) {
            auth.setType("token");
            auth.setToken(tokenFactory.getToken(tenantId, model.getTokenInfo().getIdentityKey(), false));
        }
        return auth;
    }
}
