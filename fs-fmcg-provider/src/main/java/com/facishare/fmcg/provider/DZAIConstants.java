package com.facishare.fmcg.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import org.elasticsearch.common.Strings;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/16 下午4:01
 */
public class DZAIConstants {

    private DZAIConstants() {
    }

    public static Set<String> APRON_LABEL = Sets.newHashSet("红色围裙", "橙色围裙");

    public static Set<String> TASTING_TABLE_LABEL = Sets.newHashSet("试吃台");

    public static final String APRON_API_NAME = "ai_apron__c";

    public static final String TASTING_TABLE_API_NAME = "ai_tasting_table__c";

    public static final String DETECT_PHOTOS_API_NAME = "detect_photos__c";

    public static final String DETECT_ROW_FACE_API_NAME = "detect_row_face__c";

    public static final String CONTINUOUS_ROW_FACE_API_NAME = "continuous_row_face__c";

    public static final String AI_DETECT_RESULT_API_NAME = "ai_detect_result__c";

    public static final String PRODUCT_API_NAME = "ai_product__c";

    public static final String DISPLAY_LIMIT_API_NAME = "field_P6ae6__c";

    public static final String PROOF_API_NAME = "TPMActivityProofObj";

    public static final String AI_DETECT_RESULT_OBJECT_API_NAME = "ProofAIDetectProductDetailObj__c";

    public static final String DETAIL_PRODUCT_ID = "product_id__c";

    public static final String DETAIL_NUMBER = "number__c";

    public static final String DETAIL_PROOF_ID = "proof_id__c";

    public static final String DETECT_KEY = "detect_key__c";

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            String str = iConfig.get("DZ_AI_LABEL");
            if (Strings.isNullOrEmpty(str)) {
                return;
            }
            JSONObject map = JSON.parseObject(str);
            APRON_LABEL = map.getJSONArray("APRON_LABEL").stream().map(String.class::cast).collect(Collectors.toSet());
            TASTING_TABLE_LABEL = map.getJSONArray("TASTING_TABLE_LABEL").stream().map(String.class::cast).collect(Collectors.toSet());
        });
    }
}
