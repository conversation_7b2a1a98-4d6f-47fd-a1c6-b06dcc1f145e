package com.facishare.fmcg.provider.impl.ai.detect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.ai.AIServiceAdapter;
import com.facishare.fmcg.adapter.file.FileAdapter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.detect.*;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.detect.CustomAIDetectService;
import com.facishare.fmcg.api.util.StopWatch;
import com.facishare.fmcg.provider.DZAIConstants;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.concurrent.ParallelTaskUtil;
import com.facishare.fmcg.provider.constant.RemakeMonitoringRecordFields;
import com.facishare.fmcg.provider.constant.TPMActivityProofFields;
import com.facishare.fmcg.provider.dao.abstraction.BizCallNumberDAO;
import com.facishare.fmcg.provider.dao.po.BizCallNumberPO;
import com.facishare.fmcg.provider.impl.task.TaskService;
import com.facishare.fmcg.provider.model.detect.DetectRecordDTO;
import com.facishare.fmcg.provider.task.enumeration.TaskStatusEnum;
import com.facishare.fmcg.provider.task.executor.TaskExecutorNameConstants;
import com.facishare.fmcg.provider.task.vo.TaskVO;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.FmcgAIProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.ai.QueryDetectRecord;
import com.fmcg.framework.http.contract.checkin.GetCheckinActionInfo;
import com.fmcg.framework.http.contract.checkin.UpdateActionRemarkInfo;
import com.fmcg.framework.http.contract.paas.data.*;
import com.fmcg.framework.http.util.TimeLimitedExecutor;
import com.fs.fmcg.sdk.ai.RecaptureClassifyClient;
import com.fs.fmcg.sdk.ai.adapter.SaveRecordExecutor;
import com.fs.fmcg.sdk.ai.adapter.contract.CommonDetect;
import com.fs.fmcg.sdk.ai.adapter.contract.GetModel;
import com.fs.fmcg.sdk.ai.adapter.contract.SaveDetectRecord;
import com.fs.fmcg.sdk.ai.cache.MetadataCache;
import com.fs.fmcg.sdk.ai.contract.BatchRecaptureClassify;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.fs.fmcg.sdk.ai.error.SdkFailureException;
import com.fs.fmcg.sdk.ai.factory.DetectorFactory;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.fxiaoke.common.release.GrayRelease;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.common.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/15 下午3:10
 */
@Service
@Slf4j
public class CustomAIDetectServiceImpl extends ServiceBase implements CustomAIDetectService {

    private static final String CONFIG_NAME = "fs-fmcg-framework-config";

    private static final String CONFIG_FIELD_NAME = "DZ_AI_CONFIG";

    private static Map<String, JSONObject> DZ_SCENE_MAP = new HashMap<>();

    @Resource
    private MetadataCache metadataCache;

    @Resource
    private TokenFactory tokenFactory;

    @Resource
    private FmcgAIProxy fmcgAIProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private SaveRecordExecutor saveRecordExecutor;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private RecaptureClassifyClient recaptureClassifyClient;

    @Autowired
    protected MergeJedisCmd redisCmd;

    @Resource
    private TaskService taskService;

    @Resource
    private CheckinProxy checkinProxy;

    @Resource
    private AIServiceAdapter aiServiceAdapter;

    @Resource
    private BizCallNumberDAO bizCallNumberDAO;

    @Resource
    private RedissonClient redissonCmd;

    private static final String REAL_NAME_AUTH_REDIS_KEY = "REAL_NAME_AUTH_REDIS_KEY:%s";

    private static Map<String, Integer> REAL_NAME_AUTH_TENANT_LIMIT = new HashMap<>();

    private static final String REAL_NAME_FACE_AUTH = "REAL_NAME_FACE_AUTH";


    private static final ThreadLocal<StopWatch> stopWatchThreadLocal = new ThreadLocal<>();

    static {
        ConfigFactory.getConfig(CONFIG_NAME, iConfig -> {
            String json = iConfig.get(CONFIG_FIELD_NAME);
            if (Strings.isNullOrEmpty(json))
                return;
            Map<String, JSONObject> newMap = new HashMap<>();
            JSONObject map = JSON.parseObject(json);
            map.forEach((tenantId, recordType2Scene) -> newMap.put(tenantId, (JSONObject) recordType2Scene));
            DZ_SCENE_MAP = newMap;
        });
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String json = iConfig.get("REAL_NAME_AUTH_TENANT_LIMIT");
            if (Strings.isNullOrEmpty(json)) {
                return;
            }
            REAL_NAME_AUTH_TENANT_LIMIT = JSON.parseObject(json, new TypeReference<Map<String, Integer>>() {
            });
        });

    }

    @SneakyThrows
    @Override
    public ApiResult<DZDetectByScene.Result> dzDetectByScene(ApiArg<DZDetectByScene.Arg> arg) {
        if (Boolean.TRUE.equals(arg.getData().getError())) {
            throw new FmcgException("DZ AI ERROR.", 1001011);
        }
        try {
            stopWatchThreadLocal.set(StopWatch.create(this.getClass().getSimpleName()));
            StopWatch stopWatch = stopWatchThreadLocal.get();
            stopWatch.lap("start detect ai");
            JSONObject recordType2Scene = DZ_SCENE_MAP.get(arg.getTenantId().toString());

            if (MapUtils.isEmpty(recordType2Scene)) {
                log.info("empty record map.");
                return setSuccess(new DZDetectByScene.Result());
            }
            String modelId = recordType2Scene.getString("model_id");

            JSONObject sceneData = recordType2Scene.getJSONObject(arg.getData().getRecordType());
            if (sceneData == null) {
                log.info("没有匹配的业务类型");
                return setSuccess(new DZDetectByScene.Result());
            }
            DZDetectByScene.Result result = new DZDetectByScene.Result();
            String tenantAccount = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
            SdkContext sdkContext = SdkContext.builder().tenantId(arg.getTenantId()).tenantAccount(tenantAccount).currentEmployeeId(-10000).traceId(TraceContext.get().getTraceId()).build();
            stopWatch.lap("get model");
            GetModel.ModelDTO modelDTO = metadataCache.getModel(sdkContext, modelId);
            stopWatch.lap("finish model");
            JSONObject finalData = null;

            Map<String, byte[]> pathMap = new ConcurrentHashMap<>();

            ParallelTaskUtil.ParallelTask task = ParallelTaskUtil.createParallelTask();

            stopWatch.lap("batch download image");
            arg.getData().getNPath().forEach(path -> task.submit(() -> TimeLimitedExecutor.create().execute(() -> {
                if (path.startsWith("TN") && !GrayRelease.isAllow("fmcg", "FORCE_GET_ALL_BYTES", arg.getTenantId())) {
                    pathMap.put(path, fileAdapter.downloadByChunk(tenantAccount, 1000, path));
                } else {
                    pathMap.put(path, fileAdapter.downloadAllByte(tenantAccount, 1000, path));
                }
                return 1;
            }, 8000L, new FmcgException("下载图片超时", 1000100))));

            if (!task.await(8000L, TimeUnit.MILLISECONDS)) {
                throw new FmcgException("下载图片超时", 1000100);
            }

            stopWatch.lap("batch download image");

            stopWatch.lap("start scene detect");

            try {
                switch (sceneData.getString("scene")) {
                    case "free_taste":
                        finalData = freeTaste(arg.getTenantId(), tenantAccount, modelDTO, pathMap);
                        break;
                    case "shopping_guide":
                        finalData = shoppingGuide(arg.getTenantId(), tenantAccount, modelDTO, pathMap);
                        break;
                    case "special_display":
                        finalData = specialDisplay(arg.getTenantId(), tenantAccount, modelDTO, pathMap);
                        break;
                    case "farmer_display":
                        finalData = farmerDisplay(arg.getTenantId(), tenantAccount, modelDTO, pathMap);
                        break;
                    default:
                }
            } catch (Exception ex) {
                log.info("dz ai err .", ex);
                if (ex instanceof SdkFailureException) {
                    SdkFailureException e = (SdkFailureException) ex;
                    if (e.getFailureCode() != null && e.getFailureCode().equals(401)) {
                        tokenFactory.refreshToken(modelDTO.getTokenKey());
                    }
                }
                return setFailure(new FmcgException(ex.getMessage(), 1000100));
            }
            result.setData(finalData);
            return setSuccess(result);
        } catch (TimeoutException e) {
            log.info("wait out of time");
            throw e;
        } finally {
            stopWatchThreadLocal.get().log();
            stopWatchThreadLocal.remove();
        }
    }

    @Override
    public ApiResult<SaveDZDetectRecord.Result> saveDZDetectRecord(ApiArg<SaveDZDetectRecord.Arg> arg) {
        int tenantId = arg.getTenantId();
        String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantId);
        String proofId = arg.getData().getProofId();
        PaasDataGet.Result proofResult = paasDataProxy.get(tenantId, -10000, DZAIConstants.PROOF_API_NAME, proofId, true, false);
        SaveDZDetectRecord.Result result = new SaveDZDetectRecord.Result();
        result.setSuccess(true);


        JSONArray detectedPicList = proofResult.getData().getObjectData().getJSONArray(DZAIConstants.DETECT_PHOTOS_API_NAME);

        JSONObject recordType2Scene = DZ_SCENE_MAP.get(String.valueOf(tenantId));
        String modelId = recordType2Scene.getString("model_id");
        SdkContext sdkContext = SdkContext.builder().tenantId(arg.getTenantId()).tenantAccount(tenantAccount).currentEmployeeId(-10000).traceId(TraceContext.get().getTraceId()).build();
        GetModel.ModelDTO modelDTO = metadataCache.getModel(sdkContext, modelId);
        Set<String> skuSet = modelDTO.getObjectMapList().stream().map(GetModel.ObjectMapDTO::getKey).collect(Collectors.toSet());
        skuSet.removeAll(DZAIConstants.APRON_LABEL);
        skuSet.removeAll(DZAIConstants.TASTING_TABLE_LABEL);
        Map<String, Integer> counterMap = new HashMap<>();
        Map<String, String> detectKeyMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(detectedPicList)) {
            detectedPicList.stream().map(JSONObject.class::cast).forEach(pic -> {
                String path = pic.getString("path");
                QueryDetectRecord.Arg recordArg = new QueryDetectRecord.Arg();
                recordArg.setTenantId(tenantId);
                recordArg.setPath(path);
                recordArg.setModelId(modelId);
                recordArg.setPathType(1);
                QueryDetectRecord.Result recordResult = fmcgAIProxy.queryDetectRecord(recordArg);
                if (recordResult.getRecord() == null) {
                    return;
                }
                recordResult.getRecord().getObjects().forEach(objectDto -> {
                    detectKeyMap.putIfAbsent(objectDto.getObjectId(), objectDto.getKey());
                    if (skuSet.contains(objectDto.getKey())) {
                        Integer num = counterMap.getOrDefault(objectDto.getObjectId(), 0);
                        num++;
                        counterMap.put(objectDto.getObjectId(), num);
                    }
                });
            });
        }

        if (!MapUtils.isEmpty(counterMap)) {
            List<JSONObject> data = new ArrayList<>();
            counterMap.forEach((productId, number) -> {
                JSONObject object = new JSONObject();
                object.put(DZAIConstants.DETAIL_NUMBER, number);
                object.put(DZAIConstants.DETAIL_PRODUCT_ID, productId);
                object.put(DZAIConstants.DETAIL_PROOF_ID, proofId);
                object.put(DZAIConstants.DETECT_KEY, detectKeyMap.get(productId));
                object.put("owner", Lists.newArrayList("-10000"));
                object.put("record_type", "default__c");
                data.add(object);
            });
            PaasBatchCreate.Result batchCreateResult = paasDataProxy.batchCreate(tenantId, -10000, DZAIConstants.AI_DETECT_RESULT_OBJECT_API_NAME, data);
            if (batchCreateResult.getCode() != 0) {
                log.info("batch create err.result:{},arg:{}", result, arg);
                result.setSuccess(false);
                return setSuccess(result);
            }
        }
        return setSuccess(result);
    }

    @Override
    public ApiResult<HLYProofRecapture.Result> hlyProofRecapture(ApiArg<HLYProofRecapture.Arg> arg) {
        log.info("recapture arg:{}", arg);
        String lockKey = String.format("hly_recapture:%s:%s", arg.getTenantId(), arg.getData().getProofId());
        String lockValue = UUID.randomUUID().toString();
        try {
            lock(lockKey, lockValue);
            PaasDataGet.Result getProofResult = paasDataProxy.get(arg.getTenantId(), arg.getUserId(), TPMActivityProofFields.API_NAME, arg.getData().getProofId());
            if (getProofResult.getCode() != 0) {
                throw new FmcgException(getProofResult.getMessage(), getProofResult.getCode());
            }
            JSONObject proof = getProofResult.getData().getObjectData();
            String visitId = proof.getString(TPMActivityProofFields.VISIT_ID);
            String actionId = proof.getString(TPMActivityProofFields.ACTION_ID);
            String proofId = proof.getString("_id");
            String storeId = proof.getString(TPMActivityProofFields.STORE_ID);
            String outTenantId = proof.getString("out_tenant_id");
            if (Strings.isNullOrEmpty(actionId)) {
                log.info("this data is lack of actionId.{}", proof);
                return setSuccess(new HLYProofRecapture.Result());
            }

            GetCheckinActionInfo.Result actionInfo = getRecaptureAction(arg.getTenantId(), visitId, actionId, outTenantId);
            log.info("actionInfo:{}", actionInfo);

            boolean needRecapture = arg.getData().isForceDetect() || actionInfo.getActionOpenRemake() == 1;
            if (needRecapture) {
                TypeReference<List<RecaptureImageDTO>> imageListType = new TypeReference<List<RecaptureImageDTO>>() {
                };
                List<RecaptureImageDTO> imageDTOS = proof.getObject(TPMActivityProofFields.PROOF_IMAGES, imageListType);
                imageDTOS = imageDTOS == null ? new ArrayList<>() : imageDTOS;
                List<JSONObject> recaptureObjects = queryRecaptureObjectList(arg.getTenantId(), visitId, actionId, proofId);
                List<String> needDeleteNPathObjetIds = new ArrayList<>();
                List<RecaptureImageDTO> needRecaptureImages = null;
                Map<String, String> hasRecapturedNPath2IdMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(recaptureObjects)) {
                    recaptureObjects.forEach(recaptureObj -> {
                        List<RecaptureImageDTO> imagePath = recaptureObj.getObject(RemakeMonitoringRecordFields.REMAKE_PICTURE, imageListType);
                        if (!CollectionUtils.isEmpty(imagePath)) {
                            hasRecapturedNPath2IdMap.put(imagePath.get(0).getPath(), recaptureObj.getString("_id"));
                        }
                    });
                    Set<String> targetNPathSet = imageDTOS.stream().map(RecaptureImageDTO::getPath).collect(Collectors.toSet());
                    hasRecapturedNPath2IdMap.forEach((nPath, objectId) -> {
                        if (!targetNPathSet.contains(nPath)) {
                            needDeleteNPathObjetIds.add(objectId);
                        }
                    });
                    deleteObjectData(arg.getTenantId(), RemakeMonitoringRecordFields.API_NAME, needDeleteNPathObjetIds);
                }
                if (!CollectionUtils.isEmpty(imageDTOS)) {
                    needRecaptureImages = imageDTOS.stream().filter(v -> v.getIsRecapture() == null).collect(Collectors.toList());
                    List<String> needCreateObjectPathList = recapture(arg.getTenantId(), arg.getData().getRecaptureModelId(), needRecaptureImages);
                    needCreateObjectPathList = needCreateObjectPathList.stream().filter(path -> !hasRecapturedNPath2IdMap.containsKey(path)).collect(Collectors.toList());
                    createNewRecapture(arg.getTenantId(), proofId, visitId, actionId, storeId, needCreateObjectPathList, actionInfo);
                }

                updateProofStatus(arg.getTenantId(), proofId, imageDTOS, visitId, actionId, outTenantId);
            }
            return setSuccess(new HLYProofRecapture.Result());
        } catch (Exception e) {
            log.info("hly recapture err.", e);
            if (!(e instanceof NullPointerException) && arg.getData().isCreateRetryTask()) {
                setHlyRecaptureRetryTask(arg.getTenantId(), arg.getTenantAccount(), arg.getData().getRecaptureModelId(), arg.getData().getProofId());
                return setSuccess(new HLYProofRecapture.Result(1001012, "稍后自动重试。"));
            }
            return setSuccess(new HLYProofRecapture.Result(1001011, "翻拍识别异常。"));
        } finally {
            unlock(lockKey, lockValue);
        }

    }

    @Override
    public ApiResult<RealNameFaceAuth.Result> realNameFaceAuth(ApiArg<RealNameFaceAuth.Arg> arg) {

        RLock lock = redissonCmd.getLock(String.format(REAL_NAME_AUTH_REDIS_KEY, arg.getTenantId()));
        try {
            if (!lock.tryLock(5000L, 5000L, TimeUnit.MILLISECONDS)) {
                return setFailure(600104, "请求繁忙，稍后再试。");
            }
            BizCallNumberPO bizCallNumberPO = getBizCallNumber(arg.getTenantId(), REAL_NAME_FACE_AUTH);
            String tenantId = String.valueOf(arg.getTenantId());
            if (!REAL_NAME_AUTH_TENANT_LIMIT.containsKey(tenantId)) {
                log.info("拦截非法企业请求:{}", arg);
                return setFailure(600102, "该企业不允许调用此接口。");
            }
            Integer limitNum = REAL_NAME_AUTH_TENANT_LIMIT.getOrDefault(tenantId, 0);
            if (limitNum.compareTo(bizCallNumberPO.getSuccess()) <= 0) {
                log.info("识别次数超上限:{}", bizCallNumberPO);
                return setFailure(600103, "识别次数已经用尽。");
            }
            try {
                RealNameFaceAuth.Result result = aiServiceAdapter.realNameFaceAuth(arg.getData());
                return setSuccess(result);
            } catch (FmcgException e) {
                if (e.getErrCode() != null && e.getErrCode() == 600101) {
                    bizCallNumberDAO.fail(bizCallNumberPO.getId().toString(), 1);
                } else {
                    bizCallNumberDAO.success(bizCallNumberPO.getId().toString(), 1);
                }
                return setFailure(e);
            } catch (Exception e) {
                bizCallNumberDAO.fail(bizCallNumberPO.getId().toString(), 1);
                return setFailure(600101, e.getMessage());
            }
        } catch (InterruptedException e) {
            return setFailure(600104, "发生未知异常，稍后再试。");
        }

    }

    private BizCallNumberPO getBizCallNumber(Integer tenantId, String realNameFaceAuth) {
        BizCallNumberPO po = bizCallNumberDAO.query(tenantId, realNameFaceAuth);
        if (po == null) {
            po = new BizCallNumberPO();
            po.setBiz(realNameFaceAuth);
            po.setTenantId(tenantId);
            po.setTotal(0);
            po.setSuccess(0);
            po.setFail(0);
            po.setUsers(new ArrayList<>());
            bizCallNumberDAO.save(po);
        }
        return po;
    }

    private void createNewRecapture(Integer tenantId, String proofId, String visitId, String actionId, String storeId, List<String> nPath, GetCheckinActionInfo.Result actionInfo) {
        JSONObject data = new JSONObject();
        data.put("tenant_id", tenantId);
        data.put("owner", Lists.newArrayList("-10000"));
        data.put("object_describe_api_name", RemakeMonitoringRecordFields.API_NAME);
        data.put("record_type", "default__c");
        data.put(RemakeMonitoringRecordFields.IMAGE_FIELD_NAME, "举证图片");
        data.put(RemakeMonitoringRecordFields.FALSE_SCENE, Lists.newArrayList(RemakeMonitoringRecordFields.FalseScene.REMARK));
        data.put(RemakeMonitoringRecordFields.ACTION_ID, actionId);
        data.put(RemakeMonitoringRecordFields.CHECK_ID, visitId);
        data.put(RemakeMonitoringRecordFields.ACTION_SOURCE, actionInfo.getActionName());
        data.put(RemakeMonitoringRecordFields.CHECKINSOBJ, visitId);
        data.put(RemakeMonitoringRecordFields.CUSTOMER_ID, storeId);
        data.put(RemakeMonitoringRecordFields.ASSOCIATED_REPORT_ID, proofId);
        data.put(RemakeMonitoringRecordFields.ASSOCIATED_REPORT_APINAME, TPMActivityProofFields.API_NAME);
        List<JSONObject> createObjects = new ArrayList<>();
        nPath.forEach(path -> {
            JSONObject object = data.clone();
            object.put(RemakeMonitoringRecordFields.REMAKE_PICTURE, Lists.newArrayList(new RecaptureImageDTO(path, "ext")));
            createObjects.add(object);
        });

        PaasBatchCreate.Result result = paasDataProxy.batchCreate(tenantId, -10000, RemakeMonitoringRecordFields.API_NAME, createObjects);
        if (result.getCode() == 201112011) {
            log.info("create remark err.{}", result);
            createObjects.forEach(jsonObject -> jsonObject.remove(RemakeMonitoringRecordFields.CHECKINSOBJ));
            result = paasDataProxy.batchCreate(tenantId, -10000, RemakeMonitoringRecordFields.API_NAME, createObjects);
        }
        if (result.getCode() != 0) {
            log.info("create remark err.{}", result);
            throw new FmcgException(result.getMessage(), result.getCode());
        }
    }

    private GetCheckinActionInfo.Result getRecaptureAction(Integer tenantId, String visitId, String actionId, String outTenantId) {

        GetCheckinActionInfo.Arg arg = new GetCheckinActionInfo.Arg();
        arg.setActionId(actionId);
        arg.setCheckId(visitId);
        arg.setEid(tenantId);
        if (!Strings.isNullOrEmpty(outTenantId)) {
            arg.setUpperEa(eieaConverter.enterpriseIdToAccount(tenantId));
        }
        GetCheckinActionInfo.Result result = checkinProxy.getActionInfo(tenantId, arg);
        if (result.getErrorCode() != 0) {
            log.info("get action info err.{}", result);
            throw new FmcgException(result.getMessage(), result.getErrorCode());
        }
        return result;
    }

    private void setHlyRecaptureRetryTask(Integer tenantId, String tenantAccount, String modelId, String proofId) {
        TaskVO taskVO = new TaskVO();
        taskVO.setName("hlyRecaptureRetry:" + proofId);
        taskVO.setExecutor(TaskExecutorNameConstants.HLY_RECAPTURE_EXECUTOR);
        taskVO.setPercentage(0D);
        taskVO.setPriority(0);
        taskVO.setExecuteTime(System.currentTimeMillis() + 5 * 60 * 1000);
        taskVO.setMaxConcurrentNumber(1);
        taskVO.setMaxConsumeTimes(3);
        taskVO.setReconsumeTimes(2);
        taskVO.setStatus(TaskStatusEnum.NOT_EXECUTED.value());
        Map<String, Object> params = new HashMap<>(8);
        params.put("tenant_id", tenantId);
        params.put("tenant_account", tenantAccount);
        params.put("recapture_model_id", modelId);
        params.put("proof_id", proofId);
        taskVO.setTaskParams(params);
        taskService.publish(taskVO);
    }

    private void updateProofStatus(Integer tenantId, String proofId, List<RecaptureImageDTO> imageDTOS, String visitId, String actionId, String outTenantId) {
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("_id", proofId);
        boolean recapture = imageDTOS.stream().anyMatch(v -> Boolean.TRUE.equals(v.getIsRecapture()));
        updateMap.put(TPMActivityProofFields.EXCEPTION_SITUATION, recapture ? Lists.newArrayList(TPMActivityProofFields.ExceptionSituation.MAYBE_RECAPTURE) : Lists.newArrayList());
        updateMap.put(TPMActivityProofFields.PROOF_IMAGES, imageDTOS);

        PaasDataIncrementUpdate.Arg incrementArg = new PaasDataIncrementUpdate.Arg();
        incrementArg.setData(updateMap);
        //不触发工作流
        PaasDataIncrementUpdate.Result incrementUpdateResult = paasDataProxy.incrementUpdate(tenantId, -10000, TPMActivityProofFields.API_NAME, false, incrementArg);
        if (incrementUpdateResult.getErrCode() != 0) {
            log.info("update proof status err.result:{},arg:{}", incrementUpdateResult, incrementArg);
            throw new FmcgException(incrementUpdateResult.getErrMessage(), incrementUpdateResult.getErrCode());
        }

        List<JSONObject> proofs = getProofByActionId(tenantId, visitId, actionId);
        List<UpdateActionRemarkInfo.ImageFile> updateImages = new ArrayList<>();

        proofs.forEach(proof -> {
            List<RecaptureImageDTO> recaptureImageDTOS = proof.getObject(TPMActivityProofFields.PROOF_IMAGES, new TypeReference<List<RecaptureImageDTO>>() {
            });
            if (CollectionUtils.isNotEmpty(recaptureImageDTOS)) {
                recaptureImageDTOS.forEach(recaptureImageDTO -> {
                    if (Boolean.TRUE.equals(recaptureImageDTO.getIsRecapture())) {
                        updateImages.add(UpdateActionRemarkInfo.ImageFile.builder().path(recaptureImageDTO.getPath()).recapture(recaptureImageDTO.getIsRecapture()).build());
                    }
                });
            }
        });


        UpdateActionRemarkInfo.Arg remakeImageUpdateArg = new UpdateActionRemarkInfo.Arg();
        remakeImageUpdateArg.setActionId(actionId);
        remakeImageUpdateArg.setCheckId(visitId);
        remakeImageUpdateArg.setRemakeList(updateImages);
        remakeImageUpdateArg.setEid(tenantId);
        if (!Strings.isNullOrEmpty(outTenantId)) {
            remakeImageUpdateArg.setUpperEa(eieaConverter.enterpriseIdToAccount(tenantId));
        }
        checkinProxy.updateActionRemarkInfo(tenantId, remakeImageUpdateArg);
    }

    private List<JSONObject> getProofByActionId(Integer tenantId, String visitId, String actionId) {
        PaasDataQuery.Arg queryArg = new PaasDataQuery.Arg();
        queryArg.setObjectApiName(TPMActivityProofFields.API_NAME);
        queryArg.setIncludeButtonInfo(false);
        queryArg.setIncludeDescribe(false);
        queryArg.setIncludeLayout(false);

        PaasDataQuery.QueryDTO queryDTO = new PaasDataQuery.QueryDTO();
        queryArg.setQuery(queryDTO);
        queryDTO.setLimit(100);
        queryDTO.setOffset(0);
        queryDTO.setNeedReturnCountNum(false);
        queryDTO.setSearchSource("db");
        PaasDataQuery.FilterDTO filter = new PaasDataQuery.FilterDTO(TPMActivityProofFields.ACTION_ID, "EQ", actionId);
        PaasDataQuery.FilterDTO filter2 = new PaasDataQuery.FilterDTO(TPMActivityProofFields.VISIT_ID, "EQ", visitId);
        queryDTO.setFilters(Lists.newArrayList(filter, filter2));

        PaasDataQuery.Result queryResult = paasDataProxy.query(tenantId, -10000, queryArg);
        if (queryResult.getErrCode() != 0) {
            throw new FmcgException(queryResult.getErrMessage(), queryResult.getErrCode());
        }
        return queryResult.getResult().getDataList();
    }

    private List<String> recapture(Integer tenantId, String modelId, List<RecaptureImageDTO> imageDTOS) {
        log.info("need recapture.{}", imageDTOS);
        List<String> needNewRemakePath = new ArrayList<>();
        if (!CollectionUtils.isEmpty(imageDTOS)) {
            Map<String, RecaptureImageDTO> npath2ImageMap = imageDTOS.stream().collect(Collectors.toMap(RecaptureImageDTO::getPath, Function.identity()));
            SdkContext sdkContext = SdkContext.builder().tenantId(tenantId).app("fs-fmcg-service").tenantAccount(eieaConverter.enterpriseIdToAccount(tenantId)).currentEmployeeId(-10000).build();
            BatchRecaptureClassify.Arg arg = new BatchRecaptureClassify.Arg();
            arg.setModelId(modelId);
            int maxRetry = 3;
            boolean jump;
            while (true) {
                jump = true;
                List<BatchRecaptureClassify.ImageDTO> images = imageDTOS.stream().map(recaptureImageDTO -> {
                    BatchRecaptureClassify.ImageDTO image = new BatchRecaptureClassify.ImageDTO();
                    image.setPath(recaptureImageDTO.getPath());
                    return image;
                }).collect(Collectors.toList());
                arg.setImages(images);
                BatchRecaptureClassify.Result result = recaptureClassifyClient.batchRecapture(sdkContext, arg);
                for (BatchRecaptureClassify.ImageRecaptureDTO recapture : result.getRecaptures()) {
                    if (recapture.getCode() != null) {
                        maxRetry--;
                        log.info("recapture err.{}", recapture);
                        jump = false;
                        break;
                    }
                    npath2ImageMap.get(recapture.getPath()).setIsRecapture(recapture.getRecapture());
                    if (recapture.getRecapture()) {
                        needNewRemakePath.add(recapture.getPath());
                    }
                }
                if (jump || maxRetry <= 0) {
                    break;
                }
            }
            if (!jump) {
                throw new FmcgException("超过最大重试次数", 0);
            }
        }
        return needNewRemakePath;
    }

    private void deleteObjectData(Integer tenantId, String apiName, List<String> needDeleteIds) {
        log.info("delete recapture.apiName: {},id:{}", apiName, needDeleteIds);
        if (CollectionUtils.isEmpty(needDeleteIds)) {
            return;
        }
        PaasDataBatchInvalid.Arg invalidArg = new PaasDataBatchInvalid.Arg();
        invalidArg.setIds(needDeleteIds);
        PaasDataBatchInvalid.Result invalidResult = paasDataProxy.batchInvalid(tenantId, -10000, apiName, invalidArg);
        if (invalidResult.getCode() != 0) {
            log.info("invalid err.{}", invalidResult);
            throw new FmcgException(invalidResult.getMessage(), invalidResult.getCode());
        }
        PaasDataBatchBulkDelete.Arg bulkDeleteArg = new PaasDataBatchBulkDelete.Arg();
        bulkDeleteArg.setDescribeApiName(apiName);
        bulkDeleteArg.setIdList(needDeleteIds);
        PaasDataBatchBulkDelete.Result deleteResult = paasDataProxy.batchBulkDelete(tenantId, -10000, apiName, bulkDeleteArg);
        if (deleteResult.getErrCode() != 0) {
            log.info("delete err.{}", deleteResult);
        }
    }

    private List<JSONObject> queryRecaptureObjectList(Integer tenantId, String visitId, String actionId, String proofId) {
        PaasDataQuery.Arg queryArg = new PaasDataQuery.Arg();
        queryArg.setObjectApiName(RemakeMonitoringRecordFields.API_NAME);
        queryArg.setIncludeButtonInfo(false);
        queryArg.setIncludeDescribe(false);
        queryArg.setIncludeLayout(false);

        PaasDataQuery.QueryDTO queryDTO = new PaasDataQuery.QueryDTO();
        queryArg.setQuery(queryDTO);
        queryDTO.setLimit(100);
        queryDTO.setOffset(0);
        queryDTO.setNeedReturnCountNum(false);
        queryDTO.setSearchSource("db");
        PaasDataQuery.FilterDTO filter = new PaasDataQuery.FilterDTO(RemakeMonitoringRecordFields.ACTION_ID, "EQ", actionId);
        PaasDataQuery.FilterDTO filter2 = new PaasDataQuery.FilterDTO(RemakeMonitoringRecordFields.CHECK_ID, "EQ", visitId);
        PaasDataQuery.FilterDTO proofIdFilter = new PaasDataQuery.FilterDTO(RemakeMonitoringRecordFields.ASSOCIATED_REPORT_ID, "EQ", proofId);
        PaasDataQuery.FilterDTO proofApiNameFilter = new PaasDataQuery.FilterDTO(RemakeMonitoringRecordFields.ASSOCIATED_REPORT_APINAME, "EQ", TPMActivityProofFields.API_NAME);

        queryDTO.setFilters(Lists.newArrayList(filter, filter2, proofIdFilter, proofApiNameFilter));

        PaasDataQuery.Result queryResult = paasDataProxy.query(tenantId, -10000, queryArg);
        if (queryResult.getErrCode() != 0) {
            throw new FmcgException(queryResult.getErrMessage(), queryResult.getErrCode());
        }
        return queryResult.getResult().getDataList();
    }


    private JSONObject freeTaste(Integer tenantId, String tenantAccount, GetModel.ModelDTO modelDTO, Map<String, byte[]> pathMap) throws IOException {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        JSONObject finalData = new JSONObject();
        int maxCount = 0;
        List<String> drawPath = new ArrayList<>();
        Set<String> skuSet = modelDTO.getObjectMapList().stream().map(GetModel.ObjectMapDTO::getKey).collect(Collectors.toSet());
        for (String path : pathMap.keySet()) {
            stopWatch.lap("start detect pic:" + path);
            DetectRecordDTO detectRecordDTO = detect(tenantId, tenantAccount, modelDTO, path, pathMap.get(path));
            stopWatch.lap("finish detect pic:" + path);
            drawPath.add(detectRecordDTO.getProcessPath());
            Set<String> detectedSkuSet = detectRecordDTO.getBoxDTOes().stream().map(CommonDetect.BoxDTO::getName).collect(Collectors.toSet());
            int count = 0;
            JSONObject returnData = new JSONObject();
            if (detectedSkuSet.stream().anyMatch(DZAIConstants.APRON_LABEL::contains)) {
                returnData.put(DZAIConstants.APRON_API_NAME, "true");
                detectedSkuSet.removeAll(DZAIConstants.APRON_LABEL);
                count++;
            } else {
                returnData.put(DZAIConstants.APRON_API_NAME, "false");
            }

            if (detectedSkuSet.stream().anyMatch(DZAIConstants.TASTING_TABLE_LABEL::contains)) {
                returnData.put(DZAIConstants.TASTING_TABLE_API_NAME, "true");
                detectedSkuSet.removeAll(DZAIConstants.TASTING_TABLE_LABEL);
                count++;
            } else {
                returnData.put(DZAIConstants.TASTING_TABLE_API_NAME, "false");
            }


            if (!detectedSkuSet.isEmpty() && skuSet.containsAll(detectedSkuSet)) {
                returnData.put(DZAIConstants.PRODUCT_API_NAME, "true");
                count++;
            } else {
                returnData.put(DZAIConstants.PRODUCT_API_NAME, "false");
            }


            if (count > maxCount || finalData.isEmpty()) {
                maxCount = count;
                finalData = returnData;
            }
        }
        if (maxCount == 3) {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "true");
        } else {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "false");
        }
        finalData.put(DZAIConstants.DETECT_PHOTOS_API_NAME, imageList(drawPath));
        return finalData;
    }

    private JSONObject shoppingGuide(Integer tenantId, String tenantAccount, GetModel.ModelDTO modelDTO, Map<String, byte[]> pathMap) throws IOException {
        JSONObject finalData = new JSONObject();
        int maxCount = 0;
        List<String> drawPath = new ArrayList<>();
        Set<String> skuSet = modelDTO.getObjectMapList().stream().map(GetModel.ObjectMapDTO::getKey).collect(Collectors.toSet());
        for (String path : pathMap.keySet()) {
            DetectRecordDTO detectRecordDTO = detect(tenantId, tenantAccount, modelDTO, path, pathMap.get(path));
            drawPath.add(detectRecordDTO.getProcessPath());
            Set<String> detectedSkuSet = detectRecordDTO.getBoxDTOes().stream().map(CommonDetect.BoxDTO::getName).collect(Collectors.toSet());
            int count = 0;
            JSONObject returnData = new JSONObject();
            if (detectedSkuSet.stream().anyMatch(DZAIConstants.APRON_LABEL::contains)) {
                returnData.put(DZAIConstants.APRON_API_NAME, "true");
                detectedSkuSet.removeAll(DZAIConstants.APRON_LABEL);
                count++;
            } else {
                returnData.put(DZAIConstants.APRON_API_NAME, "false");
            }

            if (!detectedSkuSet.isEmpty() && skuSet.containsAll(detectedSkuSet)) {
                returnData.put(DZAIConstants.PRODUCT_API_NAME, "true");
                count++;
            } else {
                returnData.put(DZAIConstants.PRODUCT_API_NAME, "false");
            }

            if (count > maxCount || finalData.isEmpty()) {
                maxCount = count;
                finalData = returnData;
            }
        }
        if (maxCount == 2) {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "true");
        } else {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "false");
        }
        finalData.put(DZAIConstants.DETECT_PHOTOS_API_NAME, imageList(drawPath));
        return finalData;
    }

    private JSONObject specialDisplay(Integer tenantId, String tenantAccount, GetModel.ModelDTO modelDTO, Map<String, byte[]> pathMap) throws IOException {

        JSONObject finalData = new JSONObject();
        int maxCount = 0;
        int totalRowCount = 0;
        List<String> drawPath = new ArrayList<>();
        Set<String> skuSet = modelDTO.getObjectMapList().stream().map(GetModel.ObjectMapDTO::getKey).collect(Collectors.toSet());
        skuSet.removeAll(DZAIConstants.APRON_LABEL);
        skuSet.removeAll(DZAIConstants.TASTING_TABLE_LABEL);
        for (String path : pathMap.keySet()) {
            DetectRecordDTO detectRecordDTO = detect(tenantId, tenantAccount, modelDTO, path, pathMap.get(path));
            detectRecordDTO.getBoxDTOes().removeIf(v -> !skuSet.contains(v.getName()));
            drawPath.add(detectRecordDTO.getProcessPath());
            totalRowCount += detectRecordDTO.getBoxDTOes().stream().filter(v -> skuSet.contains(v.getName())).count();
            int count = Math.max(countUsefulBoxByRow(detectRecordDTO.getBoxDTOes()), countUsefulBoxByColumn(detectRecordDTO.getBoxDTOes()));
            maxCount = Math.max(maxCount, count);
        }
        finalData.put(DZAIConstants.DETECT_ROW_FACE_API_NAME, totalRowCount);
        finalData.put(DZAIConstants.CONTINUOUS_ROW_FACE_API_NAME, maxCount);
        finalData.put(DZAIConstants.DETECT_PHOTOS_API_NAME, imageList(drawPath));
        return finalData;
    }

    private JSONObject farmerDisplay(Integer tenantId, String tenantAccount, GetModel.ModelDTO modelDTO, Map<String, byte[]> pathMap) throws IOException {
        JSONObject finalData = new JSONObject();
        int maxCount = 0;
        int totalRowCount = 0;
        List<String> drawPath = new ArrayList<>();
        Set<String> skuSet = modelDTO.getObjectMapList().stream().map(GetModel.ObjectMapDTO::getKey).collect(Collectors.toSet());
        skuSet.removeAll(DZAIConstants.APRON_LABEL);
        skuSet.removeAll(DZAIConstants.TASTING_TABLE_LABEL);
        for (String path : pathMap.keySet()) {
            DetectRecordDTO detectRecordDTO = detect(tenantId, tenantAccount, modelDTO, path, pathMap.get(path));
            detectRecordDTO.getBoxDTOes().removeIf(v -> !skuSet.contains(v.getName()));
            drawPath.add(detectRecordDTO.getProcessPath());
            totalRowCount += detectRecordDTO.getBoxDTOes().stream().filter(v -> skuSet.contains(v.getName())).count();
            int count = Math.max(countUsefulBoxByRow(detectRecordDTO.getBoxDTOes()), countUsefulBoxByColumn(detectRecordDTO.getBoxDTOes()));
            maxCount = Math.max(maxCount, count);
        }
        finalData.put(DZAIConstants.DETECT_ROW_FACE_API_NAME, totalRowCount);
        finalData.put(DZAIConstants.CONTINUOUS_ROW_FACE_API_NAME, maxCount);
        finalData.put(DZAIConstants.DETECT_PHOTOS_API_NAME, imageList(drawPath));
      /*  if (Strings.isNullOrEmpty(arg.getAgreementId())) {
            throw new FmcgException("农贸活动必须是协议活动，并签订协议", 1000101);
        }
        int limit = getLimitNumber(tenantId, arg.getAgreementId());
        if (limit <= maxCount) {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "true");
        } else {
            finalData.put(DZAIConstants.AI_DETECT_RESULT_API_NAME, "false");
        }*/
        return finalData;
    }

    private DetectRecordDTO detect(Integer tenantId, String tenantAccount, GetModel.ModelDTO modelDTO, String path, byte[] imageBytes) throws IOException {

        StopWatch stopWatch = stopWatchThreadLocal.get();
        QueryDetectRecord.Arg queryDetectRecordArg = new QueryDetectRecord.Arg();
        queryDetectRecordArg.setModelId(modelDTO.getId());
        queryDetectRecordArg.setPath(path);
        queryDetectRecordArg.setTenantId(tenantId);
        stopWatch.lap("query record");
        QueryDetectRecord.Result queryDetectRecordResult = fmcgAIProxy.queryDetectRecord(queryDetectRecordArg);
        stopWatch.lap("finish record");
        QueryDetectRecord.Record record = queryDetectRecordResult.getRecord();
        if (record == null) {


            String token = tokenFactory.getToken(modelDTO.getTokenKey());
            CommonDetect.Arg detectArg = new CommonDetect.Arg();
            detectArg.setImageBytes(imageBytes);
            detectArg.setThreshold(modelDTO.getConfidence());

            CommonDetect.Result detectResult = DetectorFactory.getDetector(modelDTO.getPlatform()).detect(modelDTO, token, detectArg);
            stopWatch.lap("finish ai direct detect");

            if (!Strings.isNullOrEmpty(detectResult.getErrorMsg())) {
                throw new FmcgException(detectResult.getErrorMsg(), 1008611);
            }

            Map<String, String> sku2productMap = modelDTO.getObjectMapList().stream().collect(Collectors.toMap(GetModel.ObjectMapDTO::getKey, GetModel.ObjectMapDTO::getObjectId, (old, newOne) -> old));

            detectResult.getResults().removeIf(v -> !sku2productMap.containsKey(v.getName()));
            Map<String, String> colorMap = modelDTO.getObjectMapList().stream().collect(Collectors.toMap(GetModel.ObjectMapDTO::getKey, GetModel.ObjectMapDTO::getColor, (left, right) -> left));
            String processPath = drawRectangle(tenantAccount, imageBytes, detectResult.getResults(), colorMap);
            stopWatch.lap("finish draw pic");

            detectResult.getResults().forEach(v -> v.setObjectId(sku2productMap.get(v.getName())));
            List<SaveDetectRecord.ObjectDTO> saveDetectRecordObjects = new ArrayList<>(transToObjectDto(detectResult.getResults()));
            saveRecordExecutor.asyncSaveDetectRecord(tenantId, -10000, modelDTO.getId(), path, processPath, saveDetectRecordObjects);
            stopWatch.lap("finish save record");
            DetectRecordDTO detectRecordDTO = new DetectRecordDTO();
            detectRecordDTO.setBoxDTOes(detectResult.getResults());
            detectRecordDTO.setProcessPath(processPath.replaceAll(".jpg", ""));

            return detectRecordDTO;
        } else {
            List<CommonDetect.BoxDTO> boxDTOes = new ArrayList<>();
            if (!CollectionUtils.isEmpty(record.getObjects())) {
                record.getObjects().forEach(objectDto -> boxDTOes.addAll(transToBoxDTO(Lists.newArrayList(objectDto))));
            }
            DetectRecordDTO detectRecordDTO = new DetectRecordDTO();
            detectRecordDTO.setBoxDTOes(boxDTOes);
            detectRecordDTO.setProcessPath(record.getProcessedPath());
            return detectRecordDTO;
        }
    }


    private List<CommonDetect.BoxDTO> transToBoxDTO(List<QueryDetectRecord.ObjectDto> objectDtos) {
        List<CommonDetect.BoxDTO> boxDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(objectDtos)) {
            objectDtos.forEach(objectDto -> {
                CommonDetect.BoxDTO boxDTO = new CommonDetect.BoxDTO();
                boxDTO.setType(objectDto.getType());
                boxDTO.setComponents(objectDto.getComponents());
                boxDTO.setIsFront(objectDto.getIsFront());
                boxDTO.setIsRotated(objectDto.getIsFront());
                boxDTO.setScore(!com.google.common.base.Strings.isNullOrEmpty(objectDto.getScore()) ? Double.parseDouble(objectDto.getScore()) : 0);
                boxDTO.setName(objectDto.getKey());
                boxDTO.setObjectId(objectDto.getObjectId());
                CommonDetect.LocationDTO locationDTO = new CommonDetect.LocationDTO();
                locationDTO.setTop(objectDto.getPosition().getY());
                locationDTO.setLeft(objectDto.getPosition().getX());
                locationDTO.setWidth(objectDto.getPosition().getW());
                locationDTO.setHeight(objectDto.getPosition().getH());
                boxDTO.setLocation(locationDTO);
                boxDTO.setComponentEntities(transToBoxDTO(objectDto.getComponentEntities()));
                boxDTOS.add(boxDTO);
            });
        }
        return boxDTOS;
    }


    private List<SaveDetectRecord.ObjectDTO> transToObjectDto(List<CommonDetect.BoxDTO> boxDTOS) {
        List<SaveDetectRecord.ObjectDTO> objectDtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(boxDTOS)) {
            boxDTOS.forEach(boxDTO -> {
                SaveDetectRecord.ObjectDTO objectDTO = new SaveDetectRecord.ObjectDTO();
                objectDTO.setType(boxDTO.getType());
                objectDTO.setComponents(boxDTO.getComponents());
                objectDTO.setIsFront(boxDTO.getIsFront());
                objectDTO.setIsRotated(boxDTO.getIsFront());
                objectDTO.setScore(boxDTO.getScore());
                objectDTO.setObjectId(boxDTO.getObjectId());
                SaveDetectRecord.PositionDTO positionDTO = new SaveDetectRecord.PositionDTO();
                positionDTO.setX(boxDTO.getLocation().getLeft());
                positionDTO.setY(boxDTO.getLocation().getTop());
                positionDTO.setW(boxDTO.getLocation().getWidth());
                positionDTO.setH(boxDTO.getLocation().getHeight());
                objectDTO.setPosition(positionDTO);
                objectDTO.setKey(boxDTO.getName());
                objectDTO.setObjectType("ProductObj");
                objectDTO.setComponentEntities(transToObjectDto(boxDTO.getComponentEntities()));
                objectDtos.add(objectDTO);
            });
        }
        return objectDtos;
    }

    private String drawRectangle(String tenantAccount, byte[] imageBytes, List<CommonDetect.BoxDTO> boxes, Map<String, String> colorMap) throws IOException {
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
        Graphics2D graphics = (Graphics2D) image.getGraphics();

        for (CommonDetect.BoxDTO box : boxes) {
            String color = colorMap.getOrDefault(box.getName(), "000000");
            graphics.setColor(new Color(Integer.valueOf(color.substring(0, 2), 16), Integer.valueOf(color.substring(2, 4), 16), Integer.valueOf(color.substring(4, 6), 16)));
            graphics.setStroke(new BasicStroke(10.0f));

            int left = (int) box.getLocation().getLeft();
            int top = (int) box.getLocation().getTop();
            int right = (int) (box.getLocation().getLeft() + box.getLocation().getWidth());
            int bottom = (int) (box.getLocation().getTop() + box.getLocation().getHeight());

            graphics.drawRoundRect(left, top, right - left, bottom - top, 50, 50);
            double size = (right - left) / 8.0;
            Font font = new Font("微软雅黑", Font.BOLD, (int) size);
            graphics.setFont(font);
            graphics.drawString(box.getName(), left + 20, top - 10);
        }

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", os);
        return fileAdapter.uploadFile(tenantAccount, -10000, os.toByteArray(), "jpg");
    }

    private List<JSONObject> imageList(List<String> paths) {
        List<JSONObject> images = new ArrayList<>();
        paths.forEach(path -> {
            JSONObject image = new JSONObject();
            image.put("ext", "jpg");
            image.put("path", path.replace(".jpg", ""));
            images.add(image);
        });
        return images;
    }


    private int countUsefulBoxByColumn(List<CommonDetect.BoxDTO> boxes) {
        boxes = boxes.stream().sorted((a, b) -> (int) (a.getLocation().getLeft() - b.getLocation().getLeft())).collect(Collectors.toList());
        List<List<CommonDetect.BoxDTO>> cols = new ArrayList<>();
        Map<CommonDetect.BoxDTO, Boolean> visitedMap = new HashMap<>();

        for (int i = 0; i < boxes.size(); i++) {
            List<CommonDetect.BoxDTO> col = new ArrayList<>();
            CommonDetect.BoxDTO baseBox = boxes.get(i);
            if (visitedMap.containsKey(baseBox))
                continue;
            col.add(baseBox);
            visitedMap.put(baseBox, true);
            double leftBound = baseBox.getLocation().getLeft();
            double rightBound = baseBox.getLocation().getLeft() + baseBox.getLocation().getWidth();

            for (int j = i + 1; j < boxes.size(); j++) {
                CommonDetect.BoxDTO now = boxes.get(j);
                double mid = now.getLocation().getLeft() + now.getLocation().getWidth() / 2;
                if (leftBound <= mid && mid <= rightBound) {
                    col.add(now);
                    visitedMap.put(now, true);
                } else {
                    break;
                }
            }
            cols.add(col.stream().sorted((a, b) -> (int) (a.getLocation().getTop() - b.getLocation().getTop())).collect(Collectors.toList()));
        }
        cols = cols.stream().sorted((a, b) -> (int) (a.get(0).getLocation().getLeft() - b.get(0).getLocation().getLeft())).collect(Collectors.toList());

        int count = 0;

        boolean outLoop = false;
        List<CommonDetect.BoxDTO> preCol = null;
        for (List<CommonDetect.BoxDTO> col : cols) {

            if (preCol != null) {
                CommonDetect.BoxDTO lastBox = preCol.get(0);
                CommonDetect.BoxDTO nowBox = col.get(0);
                if (nowBox.getLocation().getLeft() - lastBox.getLocation().getLeft() > 2 * lastBox.getLocation().getWidth()) {
                    break;
                }
            }
            preCol = col;
            count++;
            for (int i = 1; i < col.size(); i++) {
                double preNodeLoc = col.get(i - 1).getLocation().getTop() + col.get(i - 1).getLocation().getHeight();
                double nowNodeLoc = col.get(i).getLocation().getTop();
                double distance = Math.abs(nowNodeLoc - preNodeLoc);
                double avgDistance = (col.get(i - 1).getLocation().getHeight() + col.get(i).getLocation().getHeight()) / 2;
                if (distance < avgDistance) {
                    count++;
                } else {
                    outLoop = true;
                    break;
                }
            }
            if (outLoop) {
                break;
            }
        }
        //System.out.println("countUsefulBoxByColumn:"+count);
        return count;
    }

    private int countUsefulBoxByRow(List<CommonDetect.BoxDTO> boxes) {
        boxes = boxes.stream().sorted((a, b) -> (int) (a.getLocation().getTop() - b.getLocation().getTop())).collect(Collectors.toList());
        List<List<CommonDetect.BoxDTO>> rows = new ArrayList<>();
        Map<CommonDetect.BoxDTO, Boolean> visitedMap = new HashMap<>();

        for (int i = 0; i < boxes.size(); i++) {
            List<CommonDetect.BoxDTO> row = new ArrayList<>();
            CommonDetect.BoxDTO baseBox = boxes.get(i);
            if (visitedMap.containsKey(baseBox))
                continue;
            row.add(baseBox);
            visitedMap.put(baseBox, true);
            double upBound = baseBox.getLocation().getTop();
            double downBound = baseBox.getLocation().getTop() + baseBox.getLocation().getHeight();

            for (int j = 0; j < boxes.size(); j++) {
                CommonDetect.BoxDTO now = boxes.get(j);
                if (visitedMap.containsKey(now)) {
                    continue;
                }
                double mid = now.getLocation().getTop() + now.getLocation().getHeight() / 2;
                if (upBound <= mid && mid <= downBound) {
                    row.add(now);
                    visitedMap.put(now, true);
                }
            }
            rows.add(row.stream().sorted((a, b) -> (int) (a.getLocation().getLeft() - b.getLocation().getLeft())).collect(Collectors.toList()));
        }
        rows = rows.stream().sorted((a, b) -> (int) (a.get(0).getLocation().getTop() - b.get(0).getLocation().getTop())).collect(Collectors.toList());

        int count = 0;

        boolean outLoop = false;
        List<CommonDetect.BoxDTO> preRow = null;
        for (List<CommonDetect.BoxDTO> row : rows) {
            if (preRow != null) {
                CommonDetect.BoxDTO lastBox = preRow.get(0);
                CommonDetect.BoxDTO nowBox = row.get(0);
                if (nowBox.getLocation().getTop() - lastBox.getLocation().getTop() > 2 * lastBox.getLocation().getHeight()) {
                    break;
                }
            }
            count++;
            preRow = row;
            for (int i = 1; i < row.size(); i++) {
                double preNodeLoc = row.get(i - 1).getLocation().getLeft() + row.get(i - 1).getLocation().getWidth();
                double nowNodeLoc = row.get(i).getLocation().getLeft();
                double distance = Math.abs(nowNodeLoc - preNodeLoc);
                double avgDistance = (row.get(i - 1).getLocation().getWidth() + row.get(i).getLocation().getWidth()) / 2;
                if (distance < avgDistance) {
                    count++;
                } else {
                    outLoop = true;
                    break;
                }
            }
            if (outLoop) {
                break;
            }
        }
        //System.out.println("countUsefulBoxByRow:"+count);
        return count;
    }


    private Integer getLimitNumber(Integer tenantId, String agreementId) {
        PaasDataGet.Result result = paasDataProxy.get(tenantId, -10000, "TPMActivityAgreementObj", agreementId, true);
        return Double.valueOf(result.getData().getObjectData().getOrDefault(DZAIConstants.DISPLAY_LIMIT_API_NAME, 9999999).toString()).intValue();
    }

    private boolean lock(String key, String value) {
        long waitTime = 0L;
        while (!"OK".equals(redisCmd.set(key, value, SetParams.setParams().nx().ex(60L)))) {
            waitTime += 200L;
            try {
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                return false;
            }
            if (waitTime > 20000L) {
                return false;
            }
        }
        return true;
    }

    private void unlock(String key, String value) {
        redisCmd.eval("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end", Lists.newArrayList(key), Lists.newArrayList(value));
    }

}
