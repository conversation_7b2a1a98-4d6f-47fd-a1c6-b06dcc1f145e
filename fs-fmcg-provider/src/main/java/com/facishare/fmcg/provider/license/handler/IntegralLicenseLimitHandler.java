package com.facishare.fmcg.provider.license.handler;

import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auth.TPMAuthServiceImpl;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.LicenseCodeAppIdEnum;
import com.fmcg.framework.http.IntegralProxy;
import com.fmcg.framework.http.contract.integral.IntegralLicenseLimit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@SuppressWarnings("Duplicates")
@Component("integralLicenseLimitHandler")
public class IntegralLicenseLimitHandler extends IntegralLicenseHandler {
    private static final Logger log = LoggerFactory.getLogger(IntegralLicenseLimitHandler.class);

    @Resource
    private RoleInitService roleInitService;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private TPMAuthServiceImpl tpmAuthService;
    @Resource
    private IntegralProxy integralProxy;

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("start init integral limit .tenantId:{}", tenantId);


        LicensePo license = licenseDAO.get(tenantId, AppCodeEnum.INTEGRAL.code());
        if (Objects.isNull(license)) {
            log.info("init integral.");
            super.active(tenantId, tenantAccount, userId);
        }

        try {
            tpmAuthService.createRole(String.valueOf(tenantId), "积分激励", LicenseCodeAppIdEnum.INTEGRAL_LIMIT.getCode(), LicenseCodeAppIdEnum.INTEGRAL_LIMIT.getAccessRole());
        } catch (Exception e) {
            log.info("init integral limit createRole failed.", e);
        }

        roleInitService.batchAddAccessRoleUser(tenantId, LicenseCodeAppIdEnum.INTEGRAL_LIMIT.getCode());

        try {
            integralProxy.handlerOldRuleRangeEmployee(tenantId, "-10000", new IntegralLicenseLimit.Arg());
        } catch (Exception ex) {
            log.error("handlerOldRuleRangeEmployee error", ex);
        }

    }

    @Override
    public String getAppCode() {
        return AppCodeEnum.INTEGRAL_LIMIT.code();
    }
}
