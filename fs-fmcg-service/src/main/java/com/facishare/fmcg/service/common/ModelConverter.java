package com.facishare.fmcg.service.common;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.OuterApiArg;
import com.facishare.fmcg.api.model.sys.UserData;
import com.github.trace.TraceContext;

public class ModelConverter {

    private ModelConverter() {
    }

    public static <T extends ArgBase> ApiArg<T> convert(UserInfo userInfo, T data) {
        ApiArg<T> arg = new ApiArg<>();
        arg.setTenantId(userInfo.getEnterpriseId());
        arg.setTenantAccount(userInfo.getEnterpriseAccount());
        arg.setUserId(userInfo.getEmployeeId());
        arg.setData(data);
        return arg;
    }

    public static <T extends ArgBase> ApiArg<T> toServiceArg(UserData userData, T data) {
        ApiArg<T> arg = new ApiArg<>();
        arg.setTenantId(userData.getTenantId());
        arg.setUserId(userData.getUserId());
        arg.setTenantAccount(userData.getTenantAccount());
        arg.setData(data);
        return arg;
    }

    public static <T extends ArgBase> ApiArg<T> convert(UserInfo userInfo, OuterUserInfo outerUserInfo, T data) {
        ApiArg<T> arg = new ApiArg<>();
        if (isOuterRequest(outerUserInfo)) {
            arg.setTenantId(userInfo.getEnterpriseId());
            arg.setTenantAccount(userInfo.getEnterpriseAccount());
            arg.setUserId(outerUserInfo.getUpstreamOwnerId());
            arg.setOuterUserId(outerUserInfo.getOutUserId());
        } else {
            arg.setTenantId(userInfo.getEnterpriseId());
            arg.setTenantAccount(userInfo.getEnterpriseAccount());
            arg.setUserId(userInfo.getEmployeeId());
        }
        arg.setData(data);

        return arg;
    }

    public static <T extends ArgBase> OuterApiArg<T> toServiceArg(UserInfo userInfo, OuterUserInfo outerUserInfo, T data) {
        OuterApiArg<T> arg = new OuterApiArg<>();
        arg.setOuterEmployeeId(outerUserInfo.getOutUserId());
        arg.setOuterTenantId(outerUserInfo.getOutTenantId());
        arg.setAppId(outerUserInfo.getAppId());
        arg.setUpstreamOwnerId(outerUserInfo.getUpstreamOwnerId());
        arg.setUpstreamTenantId(userInfo.getEnterpriseId());
        arg.setData(data);
        return arg;
    }

    private static Boolean isOuterRequest(OuterUserInfo outerUserInfo) {
        if (outerUserInfo == null) {
            return false;
        }
        return outerUserInfo.getOutTenantId() != null && outerUserInfo.getOutUserId() != null;
    }
}
