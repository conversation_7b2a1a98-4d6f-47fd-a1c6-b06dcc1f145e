package com.facishare.fmcg.service.aop;


import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.fmcg.adapter.exception.AdapterException;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.model.sys.OuterUserData;
import com.facishare.fmcg.api.model.sys.UserData;
import com.facishare.fmcg.api.util.I18N;
import com.fs.fmcg.sdk.ai.error.SdkFailureException;
import com.google.common.base.Strings;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Aspect
@Component
public class ServiceAspect {

    private static Logger logger = LoggerFactory.getLogger(ServiceAspect.class);

    @Before(value = "execution(* com.facishare.fmcg.service.controller.facade..*(..))&& args( context, arg ) ")
    public void facadeBefore(JoinPoint joinPoint, Object context, Object arg) {
        try {
            logger.info("[Request] method - {}, Arg - {}", joinPoint.getSignature().getName(), JSON.toJSONString(arg));
        } catch (Exception e) {
            logger.info("method:{} log arg err.", joinPoint.getSignature().getName());
        }
    }

    @Before(value = "execution(* com.facishare.fmcg.service.controller.inner.*.*.*(..))&& (args(arg) || args(userData,outerUserData, arg)) ")
    public void innerBefore(JoinPoint joinPoint, UserData userData, OuterUserData outerUserData, Object arg) {
        try {
            logger.info("[Request] method - {}, Arg - {}-{}-{}", joinPoint.getSignature().getName(), JSON.toJSONString(userData), JSON.toJSONString(outerUserData), JSON.toJSONString(arg));
        } catch (Exception e) {
            logger.info("method:{} log arg err.", joinPoint.getSignature().getName());
        }
    }

    @AfterReturning(value = "execution(* com.facishare.fmcg.service.controller..*(..) ) ", returning = "rst")
    public void after(JoinPoint joinPoint, Object rst) {
        try {
            logger.info("[Response] method - {}, Result - {}", joinPoint.getSignature().getName(), JSON.toJSONString(rst));
        } catch (Exception e) {
            logger.info("method:{} log result err.", joinPoint.getSignature().getName());
        }
    }

    @Around(value = "execution(* com.facishare.fmcg.service.controller.facade..*.*(..) )&& args( context, arg ) ")
    public Object facadeAround(ProceedingJoinPoint joinPoint, Object context, Object arg) throws Throwable {
        return fmcgInvoke(joinPoint);
    }


    private Object fmcgInvoke(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (FmcgException ex) {
            logger.error("[FmcgException] - ", ex);
            ErrorCode errorCode = ErrorCode.of(ex.getErrCode());
            if (!Objects.isNull(errorCode) && !Strings.isNullOrEmpty(errorCode.getI18nKey())) {
                if (!Objects.isNull(ex.getParams())) {
                    throw new BizException(I18N.getOrDefault(errorCode.getI18nKey(), errorCode.getMessage(), ex.getParams()));
                }
                throw new BizException(I18N.getOrDefault(errorCode.getI18nKey(), errorCode.getMessage()));
            }
            throw new BizException(ex.getErrMsg(), ex.getErrCode());
        } catch (AdapterException ex) {
            logger.error("[AdapterException] - ", ex);
            ErrorCode errorCode = ErrorCode.of(ex.getErrCode());
            if (!Objects.isNull(errorCode) && !Strings.isNullOrEmpty(errorCode.getI18nKey())) {
                throw new BizException(I18N.getOrDefault(errorCode.getI18nKey(), errorCode.getMessage()));
            }
            throw new BizException(ex.getErrMsg(), ex.getErrCode());
        }
        return result;
    }

    @AfterThrowing(value = "execution(* com.facishare.fmcg.service.controller..*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Throwable e) {
        if (e instanceof FmcgException && ((FmcgException) e).getErrCode() == 1000100 || e instanceof SdkFailureException) {
            logger.info("[Exception] method - {}, Msg - {}", joinPoint.getSignature().getName(), e.getMessage());
            logger.info("[Exception] - ", e);
        } else {
            logger.error("[Exception] method - {}, Msg - {}", joinPoint.getSignature().getName(), e.getMessage());
            logger.error("[Exception] - ", e);
        }
    }

    @AfterThrowing(value = "execution(* com.facishare.fmcg.provider.impl.*.*.*(..)) || execution(* com.facishare.fmcg.provider.co.service.*.*.*(..))", throwing = "e")
    public void providerAfterThrowing(JoinPoint joinPoint, Throwable e) {
        if (e instanceof FmcgException && ((FmcgException) e).getErrCode() == 1000100 || e instanceof SdkFailureException) {
            logger.info("[Exception] method - {}, Msg - {}", joinPoint.getSignature().getName(), e.getMessage());
            logger.info("[Exception] - ", e);
        } else {
            logger.error("[Exception] method - {}, Msg - {}", joinPoint.getSignature().getName(), e.getMessage());
            logger.error("[Exception] - ", e);
        }
    }
}
