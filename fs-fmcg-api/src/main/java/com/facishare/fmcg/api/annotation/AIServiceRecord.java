package com.facishare.fmcg.api.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AI服务记录注解
 * 用于标记需要记录调用信息的AI服务方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AIServiceRecord {

    /**
     * 服务类型
     * 例如：DETECT（识别）、GENERATE（生成）、ANALYZE（分析）等
     */
    String serviceType();

    /**
     * 业务ID
     * 支持SpEL表达式，用于标识业务唯一性
     * 例如：#arg.getData().getBusinessId()
     */
    String businessId();

    /**
     * 服务描述
     */
    String description() default "";

    /**
     * 是否启用计费
     */
    boolean enableBilling() default false;

    /**
     * 计费规则
     * 例如：PER_CALL（按次计费）、PER_TIME（按时间计费）、FREE（免费）
     */
    String billingRule() default "";

    /**
     * 是否启用调用次数限制
     */
    boolean enableLimit() default false;

    /**
     * 日调用次数限制
     */
    int dailyLimit() default -1;

    /**
     * 月调用次数限制
     */
    int monthlyLimit() default -1;

    /**
     * 是否异步记录
     * 默认异步记录，不影响业务性能
     */
    boolean async() default true;

    /**
     * 是否启用结果缓存
     * 启用后，相同businessId的成功记录会被缓存并复用
     */
    boolean enableCache() default false;

    /**
     * 缓存过期时间（毫秒）
     * 默认24小时
     */
    long cacheExpireTime() default 24 * 60 * 60 * 1000;

    /**
     * 租户ID的SpEL表达式
     * 例如: "#arg.getTenantId()" 或 "#this.tenantId"
     */
    String tenantId() default "";

    /**
     * 用户ID的SpEL表达式
     * 例如: "#arg.getUserId()" 或 "#this.userId"
     */
    String userId() default "";

    /**
     * 模型ID的SpEL表达式
     * 例如: "#arg.getData().getModelId()" 或 "#this.modelId"
     */
    String modelId() default "";

    /**
     * 规则ID的SpEL表达式
     * 例如: "#arg.getData().getRuleId()" 或 "#this.ruleId"
     */
    String ruleId() default "";

    /**
     * 场景的SpEL表达式
     * 例如: "#arg.getData().getScene()" 或 "#this.scene"
     */
    String scene() default "";
} 